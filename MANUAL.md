# 오픈시스템즈 트레이딩 신호 관리 시스템 매뉴얼

## 📋 목차
1. [시스템 개요](#시스템-개요)
2. [구성 요소](#구성-요소)
3. [서비스 구조](#서비스-구조)
4. [시스템 흐름](#시스템-흐름)
5. [사용법](#사용법)
6. [배포 가이드](#배포-가이드)
7. [문제 해결](#문제-해결)

## 🎯 시스템 개요

**오픈시스템즈 트레이딩 신호 관리 시스템**은 실시간 트레이딩 신호를 감지하고 모바일 기기로 전송하는 통합 솔루션입니다.

### 주요 기능
- 실시간 로그 파일 모니터링
- 트레이딩 신호 자동 감지
- 웹 관리자 대시보드
- 모바일 신호 수신 앱
- 상태 기반 중복 신호 필터링

## 🏗️ 구성 요소

### 1. 관리자 웹 앱 (opensystems_admin)
- **경로**: `d:\augment-projects\opensystems_admin`
- **플랫폼**: Flutter Web
- **포트**: 8080
- **데이터베이스**: IndexedDB

### 2. 모바일 신호 수신 앱 (opensystems_bot_android)
- **경로**: `d:\augment-projects\opensystems_admin\opensystems_bot_android`
- **플랫폼**: Flutter Android
- **기능**: WebSocket 신호 수신, 알림, 진동

### 3. 신호 소스 파일
- **경로**: `d:/augment-projects/25_06_11/RealtimeCaptureApp/capture_log.txt`
- **역할**: 실시간 트레이딩 신호 로그 파일

## ⚙️ 서비스 구조

### 핵심 서비스

#### 1. AuthService
```dart
// 사용자 인증 관리
- login(username, password)
- logout()
- getCurrentUser()
```

#### 2. SignalDetectionService
```dart
// 신호 감지 및 처리
- startMonitoring()
- stopMonitoring()
- 상태: waiting, longActive, shortActive
- 파일: capture_log.txt 모니터링
```

#### 3. DatabaseService / WebDatabaseService
```dart
// 데이터 관리 (IndexedDB)
- getMembers()
- getTradingSignals()
- getLogEntries()
- insertMember()
```

#### 4. WebSocketService (모바일 앱)
```dart
// 실시간 신호 수신
- connect()
- onSignalReceived()
- disconnect()
```

#### 5. NotificationService (모바일 앱)
```dart
// 알림 및 진동
- showNotification()
- vibrate()
- playSound()
```

## 🔄 시스템 흐름

### 1. 초기 설정
```
1. 관리자 웹 앱 시작 (포트 8080)
2. SignalDetectionService 로그 파일 모니터링 시작
3. 모바일 앱에서 WebSocket 서버 연결
```

### 2. 신호 감지 프로세스
```
로그 파일 변화 감지
    ↓
신호 유형 파싱 (LONG/SHORT)
    ↓
상태 기반 중복 필터링
    ↓
신호 데이터베이스 저장
    ↓
WebSocket으로 모바일 앱에 전송
    ↓
모바일 앱 알림 표시
    ↓
알림 확인 후 잠자기 모드
```

### 3. 상태 기반 신호 처리
```
[대기 모드] → LONG 신호 → [롱 활성]
[롱 활성] → SHORT 신호 → [숏 활성]
[숏 활성] → LONG 신호 → [롱 활성]

* 같은 상태에서 동일 신호는 무시
* 상태 전환 시에만 새 신호로 처리
```

## 📱 사용법

### 관리자 웹 앱

#### 1. 로그인
```
1. 브라우저에서 http://localhost:8080 접속
2. 기본 계정: admin / admin123
3. 로그인 후 대시보드 화면 이동
```

#### 2. 대시보드 탭 구성

**🏠 시스템 탭**
- 서버 상태 (실행중/중지)
- 시스템 리소스 (CPU, 메모리, 디스크)
- 포트 정보 (8080)

**👥 회원 탭**
- 총 회원 수 표시
- 승인 대기 회원 수
- 회원 목록 (현재 비어있음)

**📡 신호 탭**
- 신호 감시 상태 표시
- 테스트 신호 전송 (매수/매도)
- 신호 히스토리 필터링
- 마지막 신호 정보

**📋 로그 탭**
- 로그 감시 상태
- 시스템 로그 표시

**⚙️ 설정 탭**
- 서버 포트 정보
- 로그 파일 경로
- 시스템 정보 (버전, 데이터베이스)

#### 3. 신호 모니터링 시작
```
1. 신호 탭에서 감시 상태 확인
2. 녹색 "감시중" 표시 확인
3. 로그 파일 경로 확인: d:/augment-projects/25_06_11/RealtimeCaptureApp/capture_log.txt
```

### 모바일 신호 수신 앱

#### 1. 앱 설치 및 실행
```
1. opensystems_bot_android 폴더에서 APK 빌드
2. 안드로이드 기기에 설치
3. "오픈시스템즈 봇" 앱 실행
```

#### 2. 서버 연결
```
1. 앱 실행 시 자동으로 WebSocket 서버 연결 시도
2. WiFi/4G/5G 네트워크를 통해 연결
3. 연결 상태 확인 (연결됨/연결 안됨)
```

#### 3. 신호 수신
```
1. 관리자 서버에서 신호 발생 시
2. 자동으로 푸시 알림 수신
3. 벨소리 + 진동으로 알림
4. 신호 내용 표시 (매수/매도, 심볼)
5. 확인 후 자동으로 잠자기 모드 진입
```

## 🚀 배포 가이드

### 웹 앱 배포
```bash
# 1. 의존성 설치
cd d:\augment-projects\opensystems_admin
flutter pub get

# 2. 웹 빌드
flutter build web

# 3. 로컬 서버 실행
flutter run -d chrome --web-port=8080
```

### 모바일 앱 배포
```bash
# 1. 안드로이드 앱 폴더로 이동
cd d:\augment-projects\opensystems_admin\opensystems_bot_android

# 2. 의존성 설치
flutter pub get

# 3. APK 빌드
flutter build apk --release

# 4. 생성된 APK 위치
# build/app/outputs/flutter-apk/app-release.apk
```

### 배포 파일 구조
```
downloads/
├── opensystems_admin_web/     # 웹 앱 빌드 파일
├── opensystems_bot.apk       # 모바일 앱 APK
├── README.md                  # 설치 가이드
└── MANUAL.md                  # 사용 매뉴얼
```

## 🔧 문제 해결

### 일반적인 문제

#### 1. 신호가 감지되지 않을 때
```
✅ 체크리스트:
- 로그 파일 경로 확인: d:/augment-projects/25_06_11/RealtimeCaptureApp/capture_log.txt
- 파일 존재 여부 확인
- 신호 탭에서 "감시중" 상태 확인
- 로그 파일에 새로운 내용 추가되는지 확인
```

#### 2. 모바일 앱이 신호를 받지 못할 때
```
✅ 체크리스트:
- WiFi/모바일 데이터 연결 상태 확인
- 방화벽 설정 확인 (포트 8080)
- 앱이 백그라운드에서 실행 중인지 확인
- 알림 권한 허용 여부 확인
```

#### 3. 웹 앱 접속 문제
```
✅ 체크리스트:
- 포트 8080이 다른 프로그램에서 사용 중인지 확인
- 브라우저 캐시 삭제
- 다른 브라우저로 테스트
- localhost 대신 127.0.0.1 사용
```

### 로그 확인
```
웹 앱: 브라우저 개발자 도구 → Console 탭
모바일 앱: Android Studio → Logcat
```

## 📞 지원

### 개발 환경
- Flutter SDK: 3.x 이상
- Dart: 3.x 이상
- Chrome 브라우저 (웹 앱용)
- Android Studio (모바일 앱 빌드용)

### 시스템 요구사항
- Windows 10/11
- 최소 4GB RAM
- 1GB 디스크 공간

---

**© 2025 오픈시스템즈. 모든 권리 보유.**
