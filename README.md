# opensystems_mobile

# 오픈시스템즈 트레이딩 신호 관리 시스템

Flutter 기반의 실시간 트레이딩 신호 감지 및 모바일 알림 시스템

## 🚀 빠른 시작

### 1. 웹 관리자 앱 실행
```bash
cd d:\augment-projects\opensystems_admin
flutter pub get
flutter run -d chrome --web-port=8080
```

### 2. 모바일 앱 빌드
```bash
cd opensystems_bot_android
flutter pub get
flutter build apk --release
```

### 3. 브라우저에서 접속
```
http://localhost:8080
ID: admin
PW: admin123
```

## 📁 프로젝트 구조

```
opensystems_admin/
├── lib/
│   ├── main.dart                    # 웹 앱 진입점
│   ├── screens/
│   │   ├── admin_dashboard.dart     # 관리자 대시보드
│   │   └── login_screen.dart        # 로그인 화면
│   ├── services/
│   │   ├── auth_service.dart        # 인증 서비스
│   │   ├── signal_detection_service.dart  # 신호 감지
│   │   ├── database_service.dart    # 데이터베이스 추상화
│   │   └── web_database_service.dart      # IndexedDB 구현
│   └── models/
│       ├── member.dart              # 회원 모델
│       ├── trading_signal.dart      # 신호 모델
│       └── log_entry.dart           # 로그 모델
├── opensystems_bot_android/         # 모바일 신호 수신 앱
│   ├── lib/
│   │   ├── main.dart               # 모바일 앱 진입점
│   │   ├── screens/
│   │   ├── services/
│   │   └── models/
│   └── android/
├── MANUAL.md                        # 사용 매뉴얼
├── ARCHITECTURE.md                  # 시스템 구조 문서
└── README.md                        # 이 파일
```

## 🔧 주요 기능

### 웹 관리자 앱
- ✅ 실시간 신호 감지 및 모니터링
- ✅ 관리자 대시보드 (시스템, 회원, 신호, 로그, 설정)
- ✅ 상태 기반 중복 신호 필터링
- ✅ IndexedDB 데이터 저장
- ✅ 반응형 웹 디자인 (데스크탑/모바일)

### 모바일 신호 수신 앱
- ✅ WebSocket 실시간 신호 수신
- ✅ 푸시 알림 + 벨소리 + 진동
- ✅ 백그라운드 실행
- ✅ 잠자기 모드 지원
- ✅ 4G/5G 네트워크 지원

## 📊 신호 처리 흐름

```
로그 파일 감지 → 신호 파싱 → 상태 필터링 → 데이터 저장 → WebSocket 전송 → 모바일 알림
```

## 🔍 신호 소스

**파일 경로**: `d:/augment-projects/25_06_11/RealtimeCaptureApp/capture_log.txt`

**지원 신호 형식**:
- `LONG` - 매수 신호
- `SHORT` - 매도 신호

## 📱 지원 플랫폼

- **웹**: Chrome, Firefox, Safari, Edge
- **모바일**: Android 7.0 이상
- **OS**: Windows 10/11

## 🛠️ 기술 스택

- **Frontend**: Flutter (Web/Mobile)
- **Database**: IndexedDB (브라우저)
- **Communication**: WebSocket
- **Notifications**: Flutter Local Notifications
- **State Management**: Provider
- **UI**: Flutter ScreenUtil, Responsive Framework

## 📋 요구사항

- Flutter SDK 3.x 이상
- Dart 3.x 이상
- Chrome 브라우저
- Android Studio (APK 빌드용)

## 📖 문서

- [📋 사용 매뉴얼](MANUAL.md) - 자세한 사용법
- [🏗️ 시스템 구조](ARCHITECTURE.md) - 기술적 세부사항

## 🔧 개발 명령어

```bash
# 의존성 설치
flutter pub get

# 웹 앱 개발 서버 실행
flutter run -d chrome --web-port=8080

# 웹 앱 빌드
flutter build web --release

# 모바일 앱 빌드
flutter build apk --release

# 코드 분석
flutter analyze

# 테스트 실행
flutter test
```

## 🐛 문제 해결

### 자주 발생하는 문제
1. **포트 8080 사용 중**: 다른 앱 종료 또는 포트 변경
2. **신호 미감지**: 로그 파일 경로 및 권한 확인
3. **모바일 연결 실패**: 네트워크 및 방화벽 설정 확인

### 로그 확인
- **웹**: 브라우저 개발자 도구 → Console
- **모바일**: Android Studio → Logcat

## 📞 지원

개발 관련 문의나 버그 리포트는 이슈 등록을 통해 문의해주세요.

---

**© 2025 오픈시스템즈. 모든 권리 보유.**
