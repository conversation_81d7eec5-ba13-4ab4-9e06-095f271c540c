# 오픈시스템즈 서비스 구조 및 로직 흐름

## 🏗️ 시스템 아키텍처

```
┌─────────────────────────────────────────────────────────────┐
│                    오픈시스템즈 트레이딩 신호 시스템              │
└─────────────────────────────────────────────────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   신호 소스      │    │   관리자 웹앱    │    │   모바일 앱      │
│                │    │                │    │                │
│ capture_log.txt │───▶│ opensystems_    │───▶│ opensystems_    │
│                │    │ admin           │    │ bot_android     │
│ 실시간 로그     │    │                │    │                │
│ LONG/SHORT     │    │ Flutter Web     │    │ Flutter Android │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 서비스 레이어 구조

### 1. 프레젠테이션 레이어
```
AdminDashboard (웹)          SignalReceiverScreen (모바일)
├── SystemStatusTab          ├── 신호 수신 화면
├── MemberManagementTab      ├── 알림 표시
├── SignalManagementTab      ├── 진동/소리
├── LogManagementTab         └── 잠자기 모드
└── AdminSettingsTab
```

### 2. 비즈니스 로직 레이어
```
AuthService                  SignalDetectionService
├── 로그인/로그아웃           ├── 파일 모니터링
├── 사용자 관리              ├── 신호 파싱
└── 권한 확인                ├── 상태 관리
                            └── 중복 필터링

WebSocketService             NotificationService
├── 실시간 통신              ├── 푸시 알림
├── 신호 전송                ├── 벨소리
└── 연결 관리                └── 진동
```

### 3. 데이터 레이어
```
DatabaseService              WebDatabaseService (구현체)
├── 추상 인터페이스           ├── IndexedDB 연동
└── CRUD 작업 정의          ├── 브라우저 저장소
                            └── 로컬 데이터 관리

Models
├── Member (회원)
├── TradingSignal (신호)
├── LogEntry (로그)
└── Signal (시스템 신호)
```

## 🔄 신호 처리 흐름

### 1. 신호 감지 프로세스
```
[로그 파일 모니터링]
        ↓
[파일 변경 감지]
        ↓
[새 라인 읽기]
        ↓
[신호 유형 파싱]
   ┌─────┴─────┐
   │           │
[LONG]      [SHORT]
   │           │
   └─────┬─────┘
        ↓
[상태 기반 필터링]
        ↓
[신호 저장]
        ↓
[WebSocket 전송]
```

### 2. 상태 기반 중복 필터링
```
초기 상태: WAITING
    ↓
LONG 신호 감지 → LONG_ACTIVE 상태
    ↓
동일 LONG 신호 → 무시 (중복)
    ↓
SHORT 신호 감지 → SHORT_ACTIVE 상태
    ↓
동일 SHORT 신호 → 무시 (중복)
    ↓
LONG 신호 감지 → LONG_ACTIVE 상태 (새 신호)
```

### 3. 모바일 앱 신호 처리
```
[WebSocket 연결 대기]
        ↓
[신호 수신]
        ↓
[JSON 파싱]
        ↓
[알림 생성]
   ┌─────┴─────┐
   │           │
[벨소리]    [진동]
   │           │
   └─────┬─────┘
        ↓
[화면 깨우기]
        ↓
[신호 표시]
        ↓
[사용자 확인]
        ↓
[잠자기 모드]
```

## 📝 주요 클래스 및 메서드

### SignalDetectionService
```dart
class SignalDetectionService extends ChangeNotifier {
  // 상태 관리
  SignalState _currentState = SignalState.waiting;
  bool _isMonitoring = false;
  
  // 핵심 메서드
  Future<void> startMonitoring()     // 모니터링 시작
  void stopMonitoring()              // 모니터링 중지
  void _checkLogFile()               // 로그 파일 체크
  TradingSignal? _parseLogLine()     // 로그 라인 파싱
  void _processNewSignal()           // 새 신호 처리
  
  // 상태 확인
  bool get isMonitoring => _isMonitoring;
  String? get lastSignalType => _lastSignalType;
}
```

### WebDatabaseService
```dart
class WebDatabaseService implements DatabaseService {
  // 회원 관리
  Future<List<Member>> getMembers()
  Future<void> insertMember(Member member)
  Future<Member?> getMemberByUsername(String username)
  
  // 신호 관리
  Future<List<TradingSignal>> getTradingSignals()
  Future<void> saveTradingSignal(TradingSignal signal)
  
  // 로그 관리
  Future<List<LogEntry>> getLogEntries()
  Future<void> insertLogEntry(LogEntry logEntry)
}
```

### WebSocketService (모바일)
```dart
class WebSocketService {
  WebSocketChannel? _channel;
  
  // 연결 관리
  Future<void> connect(String url)
  void disconnect()
  
  // 신호 처리
  void _handleSignal(Map<String, dynamic> data)
  Stream<TradingSignal> get signalStream
}
```

## 🔧 설정 및 구성

### 환경 설정
```dart
// 웹 앱 설정
const String WEB_PORT = '8080';
const String LOG_FILE_PATH = 'd:/augment-projects/25_06_11/RealtimeCaptureApp/capture_log.txt';

// 모바일 앱 설정
const String WEBSOCKET_URL = 'ws://localhost:8080/ws';
const Duration RECONNECT_INTERVAL = Duration(seconds: 5);
```

### 로그 파일 형식
```
예시 로그 라인:
2025-06-26 15:30:25 - LONG BTCUSDT detected
2025-06-26 15:32:10 - SHORT ETHUSDT detected

파싱 패턴:
- 시간: YYYY-MM-DD HH:MM:SS
- 신호: LONG 또는 SHORT
- 심볼: 암호화폐 쌍
```

## 📊 데이터 모델

### TradingSignal
```dart
class TradingSignal {
  final int id;                    // 고유 ID
  final String signalType;         // 'LONG' 또는 'SHORT'
  final String symbol;             // 'BTCUSDT', 'ETHUSDT' 등
  final DateTime detectedAt;       // 감지 시간
  bool processed;                  // 처리 여부
  final String source;             // 'LogFile', 'Manual', 'API'
  final String? rawLogLine;        // 원본 로그 라인
}
```

### Member
```dart
class Member {
  final int id;                    // 고유 ID
  final String username;           // 사용자명
  final String email;              // 이메일
  final String passwordHash;       // 암호화된 비밀번호
  final String role;               // 'admin', 'user'
  final DateTime createdAt;        // 생성일
  bool isActive;                   // 활성 상태
}
```

## 🚀 배포 및 실행 명령어

### 개발 환경 실행
```bash
# 웹 앱 실행
cd d:\augment-projects\opensystems_admin
flutter run -d chrome --web-port=8080

# 모바일 앱 실행 (Android)
cd opensystems_bot_android
flutter run
```

### 프로덕션 빌드
```bash
# 웹 앱 빌드
flutter build web --release

# 모바일 앱 빌드
flutter build apk --release
```

## 🔍 디버깅 및 모니터링

### 로그 레벨
```dart
enum LogLevel {
  DEBUG,    // 개발용 상세 로그
  INFO,     // 일반 정보
  WARNING,  // 경고
  ERROR,    // 오류
}
```

### 성능 모니터링
```dart
// 신호 처리 시간 측정
final stopwatch = Stopwatch()..start();
_processSignal(signal);
print('Signal processed in ${stopwatch.elapsedMilliseconds}ms');
```

---

이 문서는 오픈시스템즈 트레이딩 신호 시스템의 전체적인 구조와 흐름을 설명합니다.
상세한 사용법은 MANUAL.md를 참조하세요.
