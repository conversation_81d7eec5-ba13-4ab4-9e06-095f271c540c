@echo off
echo 오픈시스템즈 봇 모바일 앱 빌드 시작...

REM 현재 디렉토리 저장
set CURRENT_DIR=%CD%

REM 프로젝트 디렉토리로 이동
cd /d "d:\augment-projects\opensystems_admin"

echo.
echo 1. pubspec.yaml 백업 및 모바일 버전으로 교체...
if exist pubspec.yaml (
    copy pubspec.yaml pubspec_web_backup.yaml
)
copy pubspec_mobile.yaml pubspec.yaml

echo.
echo 2. 의존성 설치...
flutter pub get

echo.
echo 3. 코드 생성...
dart run build_runner build --delete-conflicting-outputs

echo.
echo 4. Android APK 빌드...
flutter build apk --release --dart-define=FLUTTER_TARGET=lib/mobile_main.dart

echo.
echo 5. APK 파일을 다운로드 폴더로 복사...
set APK_PATH=build\app\outputs\flutter-apk\app-release.apk
set DOWNLOAD_PATH=%USERPROFILE%\Downloads\opensystems_bot.apk

if exist "%APK_PATH%" (
    copy "%APK_PATH%" "%DOWNLOAD_PATH%"
    echo APK 파일이 다운로드 폴더에 복사되었습니다: %DOWNLOAD_PATH%
) else (
    echo APK 파일을 찾을 수 없습니다.
)

echo.
echo 6. pubspec.yaml 복원...
if exist pubspec_web_backup.yaml (
    copy pubspec_web_backup.yaml pubspec.yaml
    del pubspec_web_backup.yaml
)

echo.
echo 빌드 완료!
echo APK 파일 위치: %DOWNLOAD_PATH%

REM 원래 디렉토리로 복귀
cd /d "%CURRENT_DIR%"

pause
