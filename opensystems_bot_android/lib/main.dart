import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'screens/signal_receiver_screen.dart';
import 'services/signal_service.dart';
import 'services/notification_service.dart';
import 'services/background_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 권한 요청
  await requestPermissions();

  // 백그라운드 서비스 초기화
  await initializeBackgroundService();

  runApp(const OpenSystemsBotApp());
}

Future<void> requestPermissions() async {
  // 알림 권한
  await Permission.notification.isDenied.then((value) {
    if (value) {
      Permission.notification.request();
    }
  });

  // 백그라운드 실행 권한
  await Permission.systemAlertWindow.request();
  await Permission.ignoreBatteryOptimizations.request();
}

class OpenSystemsBotApp extends StatelessWidget {
  const OpenSystemsBotApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SignalService()),
            Provider(create: (_) => NotificationService()),
          ],
          child: MaterialApp(
            title: '오픈시스템즈 봇',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              fontFamily: 'NotoSansKR',
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF1976D2),
                brightness: Brightness.light,
              ),
            ),
            darkTheme: ThemeData(
              fontFamily: 'NotoSansKR',
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF1976D2),
                brightness: Brightness.dark,
              ),
            ),
            home: const SignalReceiverScreen(),
          ),
        );
      },
    );
  }
}
