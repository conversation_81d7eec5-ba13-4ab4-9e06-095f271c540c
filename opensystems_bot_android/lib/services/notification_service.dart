import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';
import 'package:vibration/vibration.dart';
import '../models/signal.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  static bool _initialized = false;

  // 1. 초기화
  static Future<void> initialize() async {
    if (_initialized) return;
    const androidInit = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidInit);
    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        assert(() {
          // print('알림 클릭: ${response.payload}');
          return true;
        }());
      },
    );
    _initialized = true;
  }

  // 2. 신호 알림 표시
  static Future<void> showSignalNotification(Signal signal) async {
    await initialize();
    const androidDetails = AndroidNotificationDetails(
      'signal_channel',
      '트레이딩 신호',
      channelDescription: '트레이딩 신호 알림',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
      autoCancel: false,
      ongoing: false,
      styleInformation: BigTextStyleInformation(''),
      category: AndroidNotificationCategory.alarm,
      fullScreenIntent: true,
      enableVibration: true,
      enableLights: true,
      ledColor: Colors.blue,
      ledOnMs: 1000,
      ledOffMs: 500,
    );
    const details = NotificationDetails(android: androidDetails);
    await _notifications.show(
      signal.id,
      signal.displayTitle,
      signal.displaySubtitle,
      details,
      payload: signal.toJson().toString(),
    );
    await _playAlertSound();
    await _vibrate();
  }

  // 3. 연결 상태 알림
  static Future<void> showConnectionNotification(String status) async {
    await initialize();
    const androidDetails = AndroidNotificationDetails(
      'status_channel',
      '연결 상태',
      channelDescription: '서버 연결 상태 알림',
      importance: Importance.low,
      priority: Priority.low,
      showWhen: true,
      autoCancel: true,
      ongoing: false,
    );
    const details = NotificationDetails(android: androidDetails);
    await _notifications.show(
      99999,
      '오픈시스템즈 봇',
      status,
      details,
    );
  }

  // 4. 모든 알림 취소
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // 5. 사운드 재생
  static Future<void> _playAlertSound() async {
    try {
      await FlutterRingtonePlayer().play(
        android: AndroidSounds.notification,
        ios: IosSounds.glass,
        looping: false,
        volume: 1.0,
        asAlarm: true,
      );
      Future.delayed(const Duration(seconds: 3), () {
        FlutterRingtonePlayer().stop();
      });
    } catch (e) {
      try {
        await FlutterRingtonePlayer().playNotification();
      } catch (e2) {
        assert(() {
          // print('대체 소리 재생 오류: $e2');
          return true;
        }());
      }
    }
  }

  // 6. 진동
  static Future<void> _vibrate() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        Vibration.vibrate(
          pattern: [0, 500, 200, 500, 200, 500],
          intensities: [0, 255, 0, 255, 0, 255],
        );
      }
    } catch (e) {
      assert(() {
        // print('진동 오류: $e');
        return true;
      }());
    }
  }
}
