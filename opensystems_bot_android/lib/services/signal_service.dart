import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart'; // debugPrint를 위해 추가
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/signal.dart';

class SignalService extends ChangeNotifier {
  static const String _serverUrl =
      'http://your-server.com/api'; // 실제 서버 URL로 변경
  static const String _signalsEndpoint = '/signals';

  List<Signal> _signals = [];
  bool _isConnected = false;
  String _connectionStatus = '연결 시도 중...';
  Timer? _connectionTimer;

  List<Signal> get signals => _signals;
  bool get isConnected => _isConnected;
  String get connectionStatus => _connectionStatus;

  SignalService() {
    _startConnectionCheck();
  }

  void _startConnectionCheck() {
    _connectionTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _checkForNewSignals(),
    );
  }

  Future<void> _checkForNewSignals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckTime = prefs.getInt('last_signal_check') ?? 0;

      final response = await http.get(
        Uri.parse('$_serverUrl$_signalsEndpoint?since=$lastCheckTime'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final newSignals = data.map((json) => Signal.fromJson(json)).toList();

        if (newSignals.isNotEmpty) {
          _signals.addAll(newSignals);
          await prefs.setInt(
            'last_signal_check',
            DateTime.now().millisecondsSinceEpoch,
          );

          // 최신 신호만 유지 (최대 100개)
          if (_signals.length > 100) {
            _signals = _signals.take(100).toList();
          }

          _isConnected = true;
          _connectionStatus = '연결됨 - 신호 수신 중';
          notifyListeners();

          // 새 신호 알림 발송
          for (final signal in newSignals) {
            await _sendNotification(signal);
          }
        } else {
          _isConnected = true;
          _connectionStatus = '연결됨 - 대기 중';
        }
      } else {
        _isConnected = false;
        _connectionStatus = '서버 오류 (${response.statusCode})';
      }
    } catch (e) {
      _isConnected = false;
      _connectionStatus = '연결 실패:  A0${e.toString()}';
      debugPrint('신호 확인 오류: $e');
    }

    notifyListeners();
  }

  Future<void> _sendNotification(Signal signal) async {
    // NotificationService를 통해 알림 발송
    // 이 부분은 NotificationService에서 처리
  }

  Future<void> manualRefresh() async {
    _connectionStatus = '새로고침 중...';
    notifyListeners();
    await _checkForNewSignals();
  }

  void clearSignals() {
    _signals.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _connectionTimer?.cancel();
    super.dispose();
  }
}
