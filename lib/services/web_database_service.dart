import 'package:localstorage/localstorage.dart';
import '../models/member.dart';
import '../models/signal.dart';
import '../models/log_entry.dart';
import '../models/system_status.dart';
import '../models/trading_signal.dart';

/// 웹 환경에서 IndexedDB와 LocalStorage를 사용하는 데이터베이스 서비스
/// Flutter 웹에서는 SharedPreferences가 LocalStorage로, LocalStorage 패키지가 IndexedDB를 사용합니다
class WebDatabaseService {
  static final WebDatabaseService _instance = WebDatabaseService._internal();
  factory WebDatabaseService() => _instance;
  WebDatabaseService._internal();

  final LocalStorage _localStorage = LocalStorage('opensystems_admin');

  Future<void> initDatabase() async {
    await _localStorage.ready;
    // IndexedDB 기반 데이터베이스 초기화 완료

    // 기본 관리자 계정 생성
    await _createDefaultAdmin();
  }

  // 기본 관리자 계정 생성
  Future<void> _createDefaultAdmin() async {
    try {
      final members = await getMembers();

      // 관리자 계정이 없으면 생성
      final adminExists = members.any((member) => member.role == 'admin');

      if (!adminExists) {
        final adminMember = Member(
          id: 1,
          username: 'admin',
          password: 'admin123', // 실제로는 암호화해야 함
          email: '<EMAIL>',
          phone: null,
          status: 'admin',
          role: 'admin',
          isActive: true,
          createdAt: DateTime.now(),
          lastLogin: null,
          pushToken: null,
        );

        await insertMember(adminMember);
        // print('기본 관리자 계정이 생성되었습니다 - ID: admin, PW: admin123');
      } else {
        // print('관리자 계정이 이미 존재합니다');
      }
    } catch (e) {
      // print('관리자 계정 생성 오류: $e');
    }
  }

  // Member 관련 메소드
  Future<List<Member>> getMembers() async {
    try {
      final membersJson = _localStorage.getItem('members') as List?;
      if (membersJson == null) return [];

      return membersJson
          .map((json) => Member.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // print('회원 목록 조회 오류: $e');
      return [];
    }
  }

  Future<void> insertMember(Member member) async {
    try {
      final members = await getMembers();
      members.add(member);

      final membersJson = members.map((m) => m.toJson()).toList();
      await _localStorage.setItem('members', membersJson);
    } catch (e) {
      // print('회원 추가 오류: $e');
    }
  }

  Future<Member?> getMemberByUsername(String username) async {
    try {
      final members = await getMembers();
      return members.firstWhere(
        (member) => member.username == username,
        orElse: () => throw StateError('Member not found'),
      );
    } catch (e) {
      return null;
    }
  }

  Future<Member?> getMemberByEmail(String email) async {
    try {
      final members = await getMembers();
      return members.firstWhere(
        (member) => member.email == email,
        orElse: () => throw StateError('Member not found'),
      );
    } catch (e) {
      return null;
    }
  }

  Future<bool> isEmailExists(String email) async {
    final member = await getMemberByEmail(email);
    return member != null;
  }

  Future<bool> isUsernameExists(String username) async {
    final member = await getMemberByUsername(username);
    return member != null;
  }

  Future<void> updateMember(Member member) async {
    try {
      final members = await getMembers();
      final index = members.indexWhere((m) => m.id == member.id);
      if (index != -1) {
        members[index] = member;
        final membersJson = members.map((m) => m.toJson()).toList();
        await _localStorage.setItem('members', membersJson);
      }
    } catch (e) {
      // print('회원 업데이트 오류: $e');
    }
  }

  Future<void> deleteMember(int id) async {
    try {
      final members = await getMembers();
      members.removeWhere((m) => m.id == id);
      final membersJson = members.map((m) => m.toJson()).toList();
      await _localStorage.setItem('members', membersJson);
    } catch (e) {
      // print('회원 삭제 오류: $e');
    }
  }

  // Signal 관련 메소드
  Future<List<Signal>> getSignals() async {
    try {
      final signalsJson = _localStorage.getItem('signals') as List?;
      if (signalsJson == null) return [];

      return signalsJson
          .map((json) => Signal.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // print('신호 목록 조회 오류: $e');
      return [];
    }
  }

  Future<void> insertSignal(Signal signal) async {
    try {
      final signals = await getSignals();
      signals.add(signal);

      // 최근 60개만 유지 - 이전 것들 자동 삭제
      if (signals.length > 60) {
        signals.removeRange(0, signals.length - 60);
      }

      final signalsJson = signals.map((s) => s.toJson()).toList();
      await _localStorage.setItem('signals', signalsJson);
    } catch (e) {
      // print('신호 추가 오류: $e');
    }
  }

  Future<void> updateSignal(Signal signal) async {
    try {
      final signals = await getSignals();
      final index = signals.indexWhere((s) => s.id == signal.id);
      if (index != -1) {
        signals[index] = signal;
        final signalsJson = signals.map((s) => s.toJson()).toList();
        await _localStorage.setItem('signals', signalsJson);
      }
    } catch (e) {
      // print('신호 업데이트 오류: $e');
    }
  }

  Future<void> deleteSignal(int id) async {
    try {
      final signals = await getSignals();
      signals.removeWhere((s) => s.id == id);
      final signalsJson = signals.map((s) => s.toJson()).toList();
      await _localStorage.setItem('signals', signalsJson);
    } catch (e) {
      // print('신호 삭제 오류: $e');
    }
  }

  // LogEntry 관련 메소드
  Future<List<LogEntry>> getLogEntries() async {
    try {
      final logsJson = _localStorage.getItem('log_entries') as List?;
      if (logsJson == null) return [];

      return logsJson
          .map((json) => LogEntry.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // print('로그 조회 오류: $e');
      return [];
    }
  }

  Future<void> insertLogEntry(LogEntry logEntry) async {
    try {
      final logs = await getLogEntries();
      logs.add(logEntry);

      // 최근 60개만 유지 - 이전 것들 자동 삭제
      if (logs.length > 60) {
        logs.removeRange(0, logs.length - 60);
      }

      final logsJson = logs.map((l) => l.toJson()).toList();
      await _localStorage.setItem('log_entries', logsJson);
    } catch (e) {
      // print('로그 추가 오류: $e');
    }
  }

  Future<void> insertLog(LogEntry logEntry) async {
    await insertLogEntry(logEntry);
  }

  Future<List<LogEntry>> getLogs({int? limit}) async {
    final logs = await getLogEntries();
    if (limit != null && logs.length > limit) {
      return logs.take(limit).toList();
    }
    return logs;
  }

  Future<void> clearLogs() async {
    try {
      await _localStorage.setItem('log_entries', []);
      // print('모든 로그가 삭제되었습니다');
    } catch (e) {
      // print('로그 삭제 오류: $e');
    }
  }

  Future<List<LogEntry>> getLogsByLevel(String level) async {
    try {
      final logs = await getLogEntries();
      return logs.where((log) => log.level == level).toList();
    } catch (e) {
      // print('레벨별 로그 조회 오류: $e');
      return [];
    }
  }

  Future<List<LogEntry>> getLogsByCategory(String category) async {
    try {
      final logs = await getLogEntries();
      return logs.where((log) => log.category == category).toList();
    } catch (e) {
      // print('카테고리별 로그 조회 오류: $e');
      return [];
    }
  }

  // TradingSignal 관련 메소드
  Future<List<TradingSignal>> getTradingSignals({int? limit}) async {
    try {
      final signalsJson = _localStorage.getItem('trading_signals') as List?;
      if (signalsJson == null) return [];

      final signals =
          signalsJson
              .map(
                (json) => TradingSignal.fromJson(json as Map<String, dynamic>),
              )
              .toList();

      if (limit != null && signals.length > limit) {
        return signals.take(limit).toList();
      }
      return signals;
    } catch (e) {
      // print('트레이딩 신호 조회 오류: $e');
      return [];
    }
  }

  Future<void> insertTradingSignal(TradingSignal signal) async {
    try {
      final signals = await getTradingSignals();
      signals.add(signal);

      // 최근 60개만 유지 - 이전 것들 자동 삭제
      if (signals.length > 60) {
        signals.removeRange(0, signals.length - 60);
      }

      final signalsJson = signals.map((s) => s.toJson()).toList();
      await _localStorage.setItem('trading_signals', signalsJson);
    } catch (e) {
      // print('트레이딩 신호 추가 오류: $e');
    }
  }

  Future<void> updateTradingSignal(TradingSignal signal) async {
    try {
      final signals = await getTradingSignals();
      final index = signals.indexWhere((s) => s.id == signal.id);
      if (index != -1) {
        signals[index] = signal;
        final signalsJson = signals.map((s) => s.toJson()).toList();
        await _localStorage.setItem('trading_signals', signalsJson);
      }
    } catch (e) {
      // print('트레이딩 신호 업데이트 오류: $e');
    }
  }

  // SystemStatus 관련 메소드
  Future<SystemStatus?> getSystemStatus() async {
    try {
      final statusJson =
          _localStorage.getItem('system_status') as Map<String, dynamic>?;
      if (statusJson == null) return null;

      return SystemStatus.fromJson(statusJson);
    } catch (e) {
      // print('시스템 상태 조회 오류: $e');
      return null;
    }
  }

  Future<void> updateSystemStatus(SystemStatus status) async {
    try {
      await _localStorage.setItem('system_status', status.toJson());
    } catch (e) {
      // print('시스템 상태 업데이트 오류: $e');
    }
  }

  // 데이터 초기화
  Future<void> clearAllData() async {
    try {
      await _localStorage.clear();
      // print('모든 데이터가 초기화되었습니다.');
    } catch (e) {
      // print('데이터 초기화 오류: $e');
    }
  }

  // 편의 메소드들
  Future<List<Member>> getAllMembers() async {
    return await getMembers();
  }

  Future<List<Signal>> getAllSignals() async {
    return await getSignals();
  }

  Future<List<TradingSignal>> getAllTradingSignals() async {
    return await getTradingSignals();
  }

  Future<List<LogEntry>> getAllLogEntries() async {
    return await getLogEntries();
  }

  // 최신 신호부터 정렬된 목록 반환
  Future<List<Signal>> getRecentSignals({int? limit}) async {
    final signals = await getSignals();
    // 시간순으로 정렬 (최신이 먼저)
    signals.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && signals.length > limit) {
      return signals.take(limit).toList();
    }
    return signals;
  }

  // 최신 트레이딩 신호부터 정렬된 목록 반환
  Future<List<TradingSignal>> getRecentTradingSignals({int? limit}) async {
    final signals = await getTradingSignals();
    // 시간순으로 정렬 (최신이 먼저)
    signals.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && signals.length > limit) {
      return signals.take(limit).toList();
    }
    return signals;
  }
}
