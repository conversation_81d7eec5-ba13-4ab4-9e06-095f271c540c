import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/trading_signal.dart';
import 'web_database_service.dart';
import 'push_service.dart';
import 'websocket_server_service.dart';

/// 실제 로그 파일을 메모리에 로드하고 모니터링하는 서비스
class RealLogMonitorService {
  static final RealLogMonitorService _instance =
      RealLogMonitorService._internal();
  factory RealLogMonitorService() => _instance;
  RealLogMonitorService._internal();

  final WebDatabaseService _databaseService = WebDatabaseService();
  final PushService _pushService = PushService();
  final WebSocketServerService _webSocketService = WebSocketServerService();

  // 메모리에 로드된 로그 내용
  String? _logContent;
  List<String> _processedLines = [];
  Timer? _monitorTimer;
  bool _isMonitoring = false;

  // 상태 getter
  bool get isMonitoring => _isMonitoring;
  int get processedLineCount => _processedLines.length;

  /// 로그 파일을 메모리에 로드
  void loadLogContent(String content) {
    _logContent = content;
    _processedLines.clear();
    debugPrint('로그 파일이 메모리에 로드됨: ${content.length} characters');
  }

  /// 실시간 모니터링 시작
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    debugPrint('=== 실제 로그 모니터링 시작 ===');

    // 3초마다 새로운 신호 체크
    _monitorTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _checkForNewSignals();
    });
  }

  /// 모니터링 중지
  void stopMonitoring() {
    _isMonitoring = false;
    _monitorTimer?.cancel();
    _monitorTimer = null;
    debugPrint('=== 실제 로그 모니터링 중지 ===');
  }

  /// 새로운 신호 체크
  void _checkForNewSignals() {
    if (_logContent == null) return;

    final lines = _logContent!.split('\n');

    // 아직 처리하지 않은 새로운 라인들 찾기
    for (int i = _processedLines.length; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isNotEmpty) {
        _processLogLine(line);
        _processedLines.add(line);
      }
    }
  }

  /// 로그 라인 파싱 및 신호 처리
  void _processLogLine(String line) {
    try {
      // 실제 로그 형식: "13:52:34  롱 신호가 감지되었습니다!"
      final signalRegex = RegExp(
        r'(\d{2}:\d{2}:\d{2})\s+(.+?)\s*신호가\s*감지되었습니다',
      );
      final match = signalRegex.firstMatch(line);

      if (match != null) {
        final timeStr = match.group(1) ?? '';
        final signalType = match.group(2) ?? '';

        _handleDetectedSignal(timeStr, signalType);
      }
    } catch (e) {
      debugPrint('로그 라인 파싱 오류: $e');
    }
  }

  /// 감지된 신호 처리
  void _handleDetectedSignal(String timeStr, String signalType) async {
    try {
      final now = DateTime.now();

      // TradingSignal 생성
      final signal = TradingSignal(
        id: now.millisecondsSinceEpoch,
        symbol: 'UNKNOWN', // 실제 로그에서 코인 정보가 없으므로
        signalType: _mapSignalType(signalType),
        detectedAt: now,
        processed: false,
        source: 'RealLog',
        rawLogLine: '$timeStr  $signalType 신호가 감지되었습니다!',
      );

      // 1️⃣ 데이터베이스에 저장
      await _databaseService.insertTradingSignal(signal);

      // 2️⃣ 어드민 화면에 출력 (웹소켓 브로드캐스트)
      _webSocketService.broadcastSignal({
        'type': 'signal',
        'data': signal.toJson(),
        'source': 'real_log',
        'message': '실제 로그에서 $signalType 신호 감지!',
      });

      // 3️⃣ 모바일 앱에 푸시 전송
      await _pushService.sendSignalNotification(
        signalType: _mapSignalType(signalType),
        coinName: 'UNKNOWN',
        message: '$signalType 신호가 감지되었습니다! (${timeStr})',
      );

      debugPrint('🚨 실제 신호 처리 완료: $signalType at $timeStr');
    } catch (e) {
      debugPrint('신호 처리 오류: $e');
    }
  }

  /// 신호 타입 매핑
  String _mapSignalType(String rawType) {
    if (rawType.contains('롱')) return 'LONG';
    if (rawType.contains('숏')) return 'SHORT';
    if (rawType.contains('매수')) return 'BUY';
    if (rawType.contains('매도')) return 'SELL';
    return 'UNKNOWN';
  }

  /// 서비스 정리
  void dispose() {
    stopMonitoring();
    _logContent = null;
    _processedLines.clear();
  }
}
