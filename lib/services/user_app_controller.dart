import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/trading_signal.dart';
import 'log_service.dart';

class UserAppController extends ChangeNotifier {
  final LogService _logService;
  
  // 등록된 사용자 앱들
  final List<UserAppClient> _registeredApps = [];
  
  UserAppController(this._logService);
  
  List<UserAppClient> get registeredApps => List.unmodifiable(_registeredApps);
  
  // 사용자 앱 등록
  Future<void> registerUserApp({
    required String appId,
    required String endpoint,
    required String pushToken,
    String? userId,
  }) async {
    try {
      final existingIndex = _registeredApps.indexWhere((app) => app.appId == appId);
      
      final userApp = UserAppClient(
        appId: appId,
        endpoint: endpoint,
        pushToken: pushToken,
        userId: userId,
        isActive: true,
        lastSeen: DateTime.now(),
      );
      
      if (existingIndex >= 0) {
        _registeredApps[existingIndex] = userApp;
      } else {
        _registeredApps.add(userApp);
      }
      
      await _logService.addLog('INFO', 'UserAppController', 
          '사용자 앱 등록: $appId');
      
      notifyListeners();
      
    } catch (e) {
      await _logService.addLog('ERROR', 'UserAppController', 
          '사용자 앱 등록 실패: $e');
    }
  }
  
  // 사용자 앱 등록 해제
  Future<void> unregisterUserApp(String appId) async {
    try {
      _registeredApps.removeWhere((app) => app.appId == appId);
      
      await _logService.addLog('INFO', 'UserAppController', 
          '사용자 앱 등록 해제: $appId');
      
      notifyListeners();
      
    } catch (e) {
      await _logService.addLog('ERROR', 'UserAppController', 
          '사용자 앱 등록 해제 실패: $e');
    }
  }
  
  // 모든 활성 사용자 앱에 신호 전송
  Future<void> broadcastSignalToAllApps(TradingSignal signal) async {
    final activeApps = _registeredApps.where((app) => app.isActive).toList();
    
    if (activeApps.isEmpty) {
      await _logService.addLog('WARNING', 'UserAppController', 
          '활성 사용자 앱이 없습니다');
      return;
    }
    
    final futures = activeApps.map((app) => sendSignalToApp(app, signal));
    await Future.wait(futures);
  }
  
  // 특정 사용자 앱에 신호 전송
  Future<bool> sendSignalToApp(UserAppClient userApp, TradingSignal signal) async {
    try {
      // 1. 앱 깨우기 (푸시 알림)
      await _wakeUpApp(userApp, signal);
      
      // 2. 신호 데이터 전송
      await _sendSignalData(userApp, signal);
      
      // 3. 매매 실행 명령
      await _executeTrade(userApp, signal);
      
      // 4. 잠자기 모드 복귀 명령
      await _putAppToSleep(userApp);
      
      // 성공 로그
      await _logService.addLog('INFO', 'UserAppController', 
          '신호 전송 완료: ${userApp.appId} - ${signal.signalType}');
      
      return true;
      
    } catch (e) {
      await _logService.addLog('ERROR', 'UserAppController', 
          '신호 전송 실패: ${userApp.appId} - $e');
      
      // 앱을 비활성 상태로 변경
      userApp.isActive = false;
      notifyListeners();
      
      return false;
    }
  }
  
  // 1단계: 앱 깨우기 (푸시 알림)
  Future<void> _wakeUpApp(UserAppClient userApp, TradingSignal signal) async {
    try {
      // FCM 푸시 알림 전송
      final payload = {
        'to': userApp.pushToken,
        'notification': {
          'title': '매매 신호 알림',
          'body': '${signal.signalType} 신호가 감지되었습니다!',
        },
        'data': {
          'action': 'WAKE_UP',
          'signal_id': signal.id.toString(),
          'signal_type': signal.signalType,
          'symbol': signal.symbol,
          'timestamp': signal.detectedAt.toIso8601String(),
        },
        'priority': 'high',
        'content_available': true,
      };
      
      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/fcm/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=YOUR_FCM_SERVER_KEY', // 실제 FCM 서버 키로 교체
        },
        body: jsonEncode(payload),
      );
      
      if (response.statusCode == 200) {
        await _logService.addLog('INFO', 'UserAppController', 
            '앱 깨우기 성공: ${userApp.appId}');
      } else {
        throw Exception('푸시 알림 전송 실패: ${response.statusCode}');
      }
      
      // 앱이 깨어날 시간 대기
      await Future.delayed(const Duration(seconds: 2));
      
    } catch (e) {
      throw Exception('앱 깨우기 실패: $e');
    }
  }
  
  // 2단계: 신호 데이터 전송
  Future<void> _sendSignalData(UserAppClient userApp, TradingSignal signal) async {
    try {
      final payload = {
        'action': 'RECEIVE_SIGNAL',
        'signal': {
          'id': signal.id,
          'type': signal.signalType,
          'symbol': signal.symbol,
          'detected_at': signal.detectedAt.toIso8601String(),
          'source': signal.source,
        },
      };
      
      final response = await http.post(
        Uri.parse('${userApp.endpoint}/api/signal'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        await _logService.addLog('INFO', 'UserAppController', 
            '신호 데이터 전송 성공: ${userApp.appId}');
      } else {
        throw Exception('신호 데이터 전송 실패: ${response.statusCode}');
      }
      
    } catch (e) {
      throw Exception('신호 데이터 전송 실패: $e');
    }
  }
  
  // 3단계: 매매 실행 명령
  Future<void> _executeTrade(UserAppClient userApp, TradingSignal signal) async {
    try {
      final payload = {
        'action': 'EXECUTE_TRADE',
        'signal_id': signal.id,
        'signal_type': signal.signalType,
        'symbol': signal.symbol,
      };
      
      final response = await http.post(
        Uri.parse('${userApp.endpoint}/api/trade'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      ).timeout(const Duration(seconds: 30));
      
      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        await _logService.addLog('INFO', 'UserAppController', 
            '매매 실행 성공: ${userApp.appId} - ${result['message'] ?? ''}');
      } else {
        throw Exception('매매 실행 실패: ${response.statusCode}');
      }
      
    } catch (e) {
      throw Exception('매매 실행 실패: $e');
    }
  }
  
  // 4단계: 잠자기 모드 복귀
  Future<void> _putAppToSleep(UserAppClient userApp) async {
    try {
      final payload = {
        'action': 'SLEEP_MODE',
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      final response = await http.post(
        Uri.parse('${userApp.endpoint}/api/sleep'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(payload),
      ).timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200) {
        await _logService.addLog('INFO', 'UserAppController', 
            '잠자기 모드 설정 성공: ${userApp.appId}');
      } else {
        await _logService.addLog('WARNING', 'UserAppController', 
            '잠자기 모드 설정 실패: ${userApp.appId} - ${response.statusCode}');
      }
      
    } catch (e) {
      await _logService.addLog('WARNING', 'UserAppController', 
          '잠자기 모드 설정 실패: ${userApp.appId} - $e');
    }
  }
  
  // 앱 상태 확인 (핑)
  Future<bool> pingApp(UserAppClient userApp) async {
    try {
      final response = await http.get(
        Uri.parse('${userApp.endpoint}/api/ping'),
      ).timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200) {
        userApp.lastSeen = DateTime.now();
        userApp.isActive = true;
        notifyListeners();
        return true;
      } else {
        userApp.isActive = false;
        notifyListeners();
        return false;
      }
      
    } catch (e) {
      userApp.isActive = false;
      notifyListeners();
      return false;
    }
  }
  
  // 모든 앱 상태 확인
  Future<void> checkAllAppsStatus() async {
    for (final app in _registeredApps) {
      await pingApp(app);
    }
  }
  
  // 비활성 앱 정리
  Future<void> cleanupInactiveApps() async {
    final now = DateTime.now();
    final threshold = now.subtract(const Duration(hours: 24));
    
    _registeredApps.removeWhere((app) => 
        !app.isActive && app.lastSeen.isBefore(threshold));
    
    notifyListeners();
  }
}

// 사용자 앱 클라이언트 정보
class UserAppClient {
  final String appId;
  final String endpoint;
  final String pushToken;
  final String? userId;
  bool isActive;
  DateTime lastSeen;
  
  UserAppClient({
    required this.appId,
    required this.endpoint,
    required this.pushToken,
    this.userId,
    required this.isActive,
    required this.lastSeen,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'app_id': appId,
      'endpoint': endpoint,
      'push_token': pushToken,
      'user_id': userId,
      'is_active': isActive,
      'last_seen': lastSeen.toIso8601String(),
    };
  }
  
  factory UserAppClient.fromJson(Map<String, dynamic> json) {
    return UserAppClient(
      appId: json['app_id'],
      endpoint: json['endpoint'],
      pushToken: json['push_token'],
      userId: json['user_id'],
      isActive: json['is_active'] ?? false,
      lastSeen: DateTime.parse(json['last_seen']),
    );
  }
}
