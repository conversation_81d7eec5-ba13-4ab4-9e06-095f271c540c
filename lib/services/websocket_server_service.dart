import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Flutter 웹 환경에서 WebSocket 서버 역할을 하는 서비스
class WebSocketServerService extends ChangeNotifier {
  static const String _wsPort = '8081';

  final List<dynamic> _connectedClients = [];
  bool _isRunning = false;
  StreamSubscription? _messageSubscription;

  bool get isRunning => _isRunning;
  int get connectedClientsCount => _connectedClients.length;

  /// 서버 시작
  Future<void> startServer() async {
    if (_isRunning) return;

    try {
      _isRunning = true;
      notifyListeners();

      if (kDebugMode) {
        print('WebSocket 서버가 포트 $_wsPort에서 시작되었습니다.');
      }
    } catch (e) {
      _isRunning = false;
      notifyListeners();
      if (kDebugMode) {
        print('WebSocket 서버 시작 실패: $e');
      }
      rethrow;
    }
  }

  /// 서버 중지
  Future<void> stopServer() async {
    if (!_isRunning) return;

    try {
      // 모든 클라이언트 연결 종료
      for (final client in _connectedClients) {
        client.close();
      }
      _connectedClients.clear();

      await _messageSubscription?.cancel();
      _messageSubscription = null;

      _isRunning = false;
      notifyListeners();

      if (kDebugMode) {
        print('WebSocket 서버가 중지되었습니다.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WebSocket 서버 중지 중 오류: $e');
      }
    }
  }

  /// 신호를 모든 연결된 클라이언트에게 브로드캐스트
  void broadcastSignal(Map<String, dynamic> signal) {
    if (!_isRunning) return;

    final message = jsonEncode({
      'type': 'signal',
      'data': signal,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // 실제 환경에서는 WebSocket을 통해 전송하지만,
    // 웹 환경에서는 브라우저 이벤트를 통해 시뮬레이션
    _simulateBroadcast(message);

    if (kDebugMode) {
      print('신호 브로드캐스트: ${signal['signalType']} - ${signal['symbol']}');
    }
  }
  /// 웹 환경에서 브로드캐스트 시뮬레이션
  void _simulateBroadcast(String message) {
    try {
      // 웹 환경에서만 브라우저 커스텀 이벤트를 통한 신호 전달
      if (kIsWeb) {
        // html.CustomEvent('opensystems_signal', detail: message);
        // html.window.dispatchEvent(event);
        print('Broadcast signal to web: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('브로드캐스트 시뮬레이션 오류: $e');
      }
    }
  }

  /// 클라이언트 연결 추가 (시뮬레이션)
  void addClient(String clientId) {
    if (kDebugMode) {
      print('클라이언트 연결됨: $clientId');
    }
    notifyListeners();
  }

  /// 클라이언트 연결 제거 (시뮬레이션)
  void removeClient(String clientId) {
    if (kDebugMode) {
      print('클라이언트 연결 해제됨: $clientId');
    }
    notifyListeners();
  }

  /// 테스트 신호 전송
  void sendTestSignal(String signalType, String symbol) {
    final signal = {
      'signalType': signalType,
      'symbol': symbol,
      'timestamp': DateTime.now().toIso8601String(),
      'source': 'WebSocketServer',
      'priority': signalType == 'LONG' ? 'HIGH' : 'MEDIUM',
    };

    broadcastSignal(signal);
  }

  @override
  void dispose() {
    stopServer();
    super.dispose();
  }
}
