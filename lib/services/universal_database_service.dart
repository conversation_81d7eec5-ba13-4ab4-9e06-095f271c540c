import 'web_database_service.dart';
import '../models/member.dart';
import '../models/signal.dart';
import '../models/log_entry.dart';
import '../models/system_status.dart';
import '../models/trading_signal.dart';

class UniversalDatabaseService {
  static UniversalDatabaseService? _instance;
  static UniversalDatabaseService get instance =>
      _instance ??= UniversalDatabaseService._();

  UniversalDatabaseService._();

  late final WebDatabaseService _webDb;

  Future<void> init() async {
    _webDb = WebDatabaseService();
    await _webDb.initDatabase();
  }

  // Members
  Future<void> insertMember(Member member) async {
    return await _webDb.insertMember(member);
  }

  Future<Member?> getMemberByUsername(String username) async {
    return await _webDb.getMemberByUsername(username);
  }

  Future<List<Member>> getAllMembers() async {
    return await _webDb.getAllMembers();
  }

  // Signals
  Future<void> insertSignal(Signal signal) async {
    return await _webDb.insertSignal(signal);
  }

  Future<List<Signal>> getAllSignals() async {
    return await _webDb.getAllSignals();
  }

  // Trading Signals
  Future<void> insertTradingSignal(TradingSignal signal) async {
    return await _webDb.insertTradingSignal(signal);
  }

  Future<List<TradingSignal>> getAllTradingSignals() async {
    return await _webDb.getAllTradingSignals();
  }

  // Log Entries
  Future<void> insertLogEntry(LogEntry logEntry) async {
    return await _webDb.insertLogEntry(logEntry);
  }

  Future<List<LogEntry>> getAllLogEntries() async {
    return await _webDb.getAllLogEntries();
  }

  // System Status
  Future<void> updateSystemStatus(SystemStatus status) async {
    return await _webDb.updateSystemStatus(status);
  }

  Future<SystemStatus?> getSystemStatus() async {
    return await _webDb.getSystemStatus();
  }
}
