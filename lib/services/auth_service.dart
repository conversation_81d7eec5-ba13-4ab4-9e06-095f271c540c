import 'package:flutter/foundation.dart';
import 'web_database_service.dart';
import '../models/member.dart';
import 'log_service.dart';

class AuthService extends ChangeNotifier {
  final WebDatabaseService _databaseService = WebDatabaseService();
  final LogService _logService = LogService();
  
  Member? _currentUser;
  bool _isLoading = false;
  
  Member? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  bool get isAdmin => _currentUser?.status == 'admin';
  bool get isLoading => _isLoading;

  // 로그인
  Future<bool> login(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final member = await _databaseService.getMemberByUsername(username);
      
      if (member == null) {
        await _logService.addLog('WARNING', 'AuthService', '존재하지 않는 사용자: $username');
        _isLoading = false;
        notifyListeners();
        return false;
      }

      if (!member.isActive) {
        await _logService.addLog('WARNING', 'AuthService', '비활성화된 사용자: $username');
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // 실제로는 해시화된 비밀번호와 비교해야 함
      if (member.password != password) {
        await _logService.addLog('WARNING', 'AuthService', '잘못된 비밀번호: $username');
        _isLoading = false;
        notifyListeners();
        return false;
      }

      _currentUser = member;
      
      // 마지막 로그인 시간 업데이트
      final updatedMember = member.copyWith(lastLogin: DateTime.now());
      await _databaseService.updateMember(updatedMember);
      _currentUser = updatedMember;
      
      await _logService.addLog('INFO', 'AuthService', '로그인 성공: $username (${member.role})');
      _isLoading = false;
      notifyListeners();
      return true;
      
    } catch (e) {
      await _logService.addLog('ERROR', 'AuthService', '로그인 오류: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // 회원가입
  Future<bool> register(String username, String email, String password, {String? phone}) async {
    _isLoading = true;
    notifyListeners();

    try {
      // 중복 확인
      final existingUser = await _databaseService.getMemberByUsername(username);
      if (existingUser != null) {
        await _logService.addLog('WARNING', 'AuthService', '이미 존재하는 사용자명: $username');
        _isLoading = false;
        notifyListeners();
        return false;
      }

      final existingEmail = await _databaseService.getMemberByEmail(email);
      if (existingEmail != null) {
        await _logService.addLog('WARNING', 'AuthService', '이미 존재하는 이메일: $email');
        _isLoading = false;
        notifyListeners();
        return false;
      }

      // 새 회원 생성
      final newMember = Member(
        id: 0,
        username: username,
        password: password, // 실제로는 해시화해야 함
        email: email,
        phone: phone,
        status: 'pending', // 승인 대기 상태
        role: 'user', // 기본값은 일반 사용자
        isActive: false, // 승인 후 활성화
        createdAt: DateTime.now(),
        lastLogin: null,
      );

      await _databaseService.insertMember(newMember);
      await _logService.addLog('INFO', 'AuthService', '회원가입 성공: $username');
      
      _isLoading = false;
      notifyListeners();
      return true;
      
    } catch (e) {
      await _logService.addLog('ERROR', 'AuthService', '회원가입 오류: $e');
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // 로그아웃
  Future<void> logout() async {
    if (_currentUser != null) {
      await _logService.addLog('INFO', 'AuthService', '로그아웃: ${_currentUser!.username}');
      _currentUser = null;
      notifyListeners();
    }
  }

  // 비밀번호 변경
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    if (_currentUser == null) return false;

    try {
      if (_currentUser!.password != currentPassword) {
        await _logService.addLog('WARNING', 'AuthService', '현재 비밀번호 불일치: ${_currentUser!.username}');
        return false;
      }

      _currentUser!.password = newPassword; // 실제로는 해시화해야 함
      await _databaseService.updateMember(_currentUser!);
      
      await _logService.addLog('INFO', 'AuthService', '비밀번호 변경 성공: ${_currentUser!.username}');
      return true;
      
    } catch (e) {
      await _logService.addLog('ERROR', 'AuthService', '비밀번호 변경 오류: $e');
      return false;
    }
  }

  // 사용자 정보 업데이트
  Future<bool> updateUserInfo(String email) async {
    if (_currentUser == null) return false;

    try {
      // 이메일 중복 확인 (본인 제외)
      final existingEmail = await _databaseService.getMemberByEmail(email);
      if (existingEmail != null && existingEmail.id != _currentUser!.id) {
        await _logService.addLog('WARNING', 'AuthService', '이미 존재하는 이메일: $email');
        return false;
      }

      _currentUser!.email = email;
      await _databaseService.updateMember(_currentUser!);
      
      await _logService.addLog('INFO', 'AuthService', '사용자 정보 업데이트: ${_currentUser!.username}');
      return true;
      
    } catch (e) {
      await _logService.addLog('ERROR', 'AuthService', '사용자 정보 업데이트 오류: $e');
      return false;
    }
  }

  // 현재 사용자 새로고침
  Future<void> refreshCurrentUser() async {
    if (_currentUser != null) {
      final updated = await _databaseService.getMemberByUsername(_currentUser!.username);
      if (updated != null) {
        _currentUser = updated;
      }
    }
  }
}
