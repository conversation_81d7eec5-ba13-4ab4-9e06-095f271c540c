import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/trading_signal.dart';
import 'database_service.dart';
import 'log_service.dart';
import 'push_service.dart';

class TradingAppController extends ChangeNotifier {
  final DatabaseService _databaseService;
  final LogService _logService;
  final PushService _pushService;

  Timer? _wakeupTimer;
  Timer? _sleepTimer;
  bool _isAppAwake = false;
  String? _targetAppUrl;
  Map<String, dynamic> _appConfig = {};

  // 앱 상태 관리
  static const int _wakeupTimeoutSeconds = 60; // 1분 후 자동 잠자기
  static const int _tradingTimeoutSeconds = 30; // 매매 완료 대기 시간

  TradingAppController(
    this._databaseService,
    this._logService,
    this._pushService,
  );

  bool get isAppAwake => _isAppAwake;
  String? get targetAppUrl => _targetAppUrl;

  // 타겟 앱 설정
  void setTargetApp(String url, Map<String, dynamic> config) {
    _targetAppUrl = url;
    _appConfig = config;
    notifyListeners();
  }

  // 신호 발생 시 앱 깨우기 및 매매 실행
  Future<void> wakeupAndTrade(TradingSignal signal) async {
    try {
      await _logService.addLog(
        'INFO',
        'TradingController',
        '앱 깨우기 시작: ${signal.signalType} 신호',
      );

      // 1단계: 푸시 알림으로 앱 깨우기
      await _sendWakeupPush(signal);

      // 2단계: HTTP 요청으로 앱 활성화 확인
      final wakeupSuccess = await _sendWakeupRequest(signal);

      if (wakeupSuccess) {
        _isAppAwake = true;
        notifyListeners();

        // 3단계: 매매 신호 전송
        await _sendTradingSignal(signal);

        // 4단계: 매매 완료 대기 및 잠자기 모드 전환
        _startTradingTimeout(signal);
      } else {
        await _logService.addLog(
          'ERROR',
          'TradingController',
          '앱 깨우기 실패: 타겟 앱이 응답하지 않음',
        );
      }
    } catch (e) {
      await _logService.addLog(
        'ERROR',
        'TradingController',
        '앱 깨우기 및 매매 실행 실패: $e',
      );
    }
  }

  // 푸시 알림으로 앱 깨우기
  Future<void> _sendWakeupPush(TradingSignal signal) async {
    try {
      await _pushService.sendPushNotification(
        title: '🚨 매매 신호 알림',
        body: '${signal.signalType} 신호 감지! 앱을 깨웁니다.',
        data: {
          'action': 'WAKEUP_AND_TRADE',
          'signal_type': signal.signalType,
          'symbol': signal.symbol,
          'detected_at': signal.detectedAt.toIso8601String(),
          'priority': 'high',
          'wake_lock': 'true',
        },
      );

      await _logService.addLog(
        'INFO',
        'TradingController',
        '푸시 알림 전송 완료: WAKEUP_AND_TRADE',
      );
    } catch (e) {
      await _logService.addLog('ERROR', 'TradingController', '푸시 알림 전송 실패: $e');
    }
  }

  // HTTP 요청으로 앱 깨우기 확인
  Future<bool> _sendWakeupRequest(TradingSignal signal) async {
    if (_targetAppUrl == null) return false;

    try {
      final response = await http
          .post(
            Uri.parse('$_targetAppUrl/wakeup'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': _appConfig['auth_token'] ?? '',
            },
            body: jsonEncode({
              'action': 'WAKEUP',
              'signal': {
                'type': signal.signalType,
                'symbol': signal.symbol,
                'timestamp': signal.detectedAt.toIso8601String(),
              },
              'config': _appConfig,
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        await _logService.addLog(
          'INFO',
          'TradingController',
          '앱 깨우기 성공: ${responseData['message'] ?? 'OK'}',
        );
        return true;
      } else {
        await _logService.addLog(
          'WARNING',
          'TradingController',
          '앱 깨우기 실패: HTTP ${response.statusCode}',
        );
        return false;
      }
    } catch (e) {
      await _logService.addLog(
        'ERROR',
        'TradingController',
        'HTTP 깨우기 요청 실패: $e',
      );
      return false;
    }
  }

  // 매매 신호 전송
  Future<void> _sendTradingSignal(TradingSignal signal) async {
    if (_targetAppUrl == null) return;

    try {
      final response = await http
          .post(
            Uri.parse('$_targetAppUrl/trade'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': _appConfig['auth_token'] ?? '',
            },
            body: jsonEncode({
              'action': 'EXECUTE_TRADE',
              'signal': {
                'type': signal.signalType,
                'symbol': signal.symbol,
                'timestamp': signal.detectedAt.toIso8601String(),
                'amount': _appConfig['trade_amount'] ?? 100,
                'leverage': _appConfig['leverage'] ?? 1,
                'mode': _appConfig['mode'] ?? 'demo', // demo/real
              },
              'risk_management': {
                'stop_loss': _appConfig['stop_loss'],
                'take_profit': _appConfig['take_profit'],
                'max_position_size': _appConfig['max_position_size'],
              },
            }),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // 신호 처리 완료 표시
        signal.processed = true;
        await _databaseService.updateTradingSignal(signal);

        await _logService.addLog(
          'INFO',
          'TradingController',
          '매매 신호 전송 성공: ${responseData['trade_id'] ?? 'Unknown'}',
        );

        // 매매 완료 푸시 알림
        await _pushService.sendPushNotification(
          title: '✅ 매매 완료',
          body: '${signal.signalType} 매매가 실행되었습니다.',
          data: {'action': 'TRADE_COMPLETED', 'trade_result': responseData},
        );
      } else {
        await _logService.addLog(
          'ERROR',
          'TradingController',
          '매매 신호 전송 실패: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      await _logService.addLog('ERROR', 'TradingController', '매매 신호 전송 실패: $e');
    }
  }

  // 매매 완료 대기 및 타임아웃 설정
  void _startTradingTimeout(TradingSignal signal) {
    // 기존 타이머 정리
    _sleepTimer?.cancel();

    _sleepTimer = Timer(Duration(seconds: _tradingTimeoutSeconds), () async {
      await _sendSleepCommand();
    });
  }

  // 앱을 잠자기 모드로 전환
  Future<void> _sendSleepCommand() async {
    if (_targetAppUrl == null) return;

    try {
      final response = await http
          .post(
            Uri.parse('$_targetAppUrl/sleep'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': _appConfig['auth_token'] ?? '',
            },
            body: jsonEncode({
              'action': 'SLEEP_MODE',
              'timestamp': DateTime.now().toIso8601String(),
            }),
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        _isAppAwake = false;
        notifyListeners();

        await _logService.addLog('INFO', 'TradingController', '앱 잠자기 모드 전환 완료');

        // 잠자기 모드 전환 푸시 알림
        await _pushService.sendPushNotification(
          title: '😴 잠자기 모드',
          body: '매매 완료 후 앱이 잠자기 모드로 전환되었습니다.',
          data: {'action': 'SLEEP_MODE_ACTIVATED'},
        );
      } else {
        await _logService.addLog(
          'WARNING',
          'TradingController',
          '잠자기 모드 전환 실패: HTTP ${response.statusCode}',
        );
      }
    } catch (e) {
      await _logService.addLog(
        'ERROR',
        'TradingController',
        '잠자기 모드 전환 실패: $e',
      );
    }
  }

  // 수동으로 앱 깨우기
  Future<void> manualWakeup() async {
    if (_targetAppUrl == null) return;

    try {
      final response = await http
          .post(
            Uri.parse('$_targetAppUrl/wakeup'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': _appConfig['auth_token'] ?? '',
            },
            body: jsonEncode({
              'action': 'MANUAL_WAKEUP',
              'timestamp': DateTime.now().toIso8601String(),
            }),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        _isAppAwake = true;
        notifyListeners();

        await _logService.addLog('INFO', 'TradingController', '수동 앱 깨우기 성공');

        // 자동 잠자기 타이머 시작
        _startAutoSleepTimer();
      }
    } catch (e) {
      await _logService.addLog('ERROR', 'TradingController', '수동 앱 깨우기 실패: $e');
    }
  }

  // 수동으로 앱 잠자기
  Future<void> manualSleep() async {
    await _sendSleepCommand();
  }

  // 자동 잠자기 타이머 시작
  void _startAutoSleepTimer() {
    _wakeupTimer?.cancel();

    _wakeupTimer = Timer(Duration(seconds: _wakeupTimeoutSeconds), () async {
      await _sendSleepCommand();
    });
  }

  // 앱 상태 확인
  Future<Map<String, dynamic>?> checkAppStatus() async {
    if (_targetAppUrl == null) return null;

    try {
      final response = await http
          .get(
            Uri.parse('$_targetAppUrl/status'),
            headers: {'Authorization': _appConfig['auth_token'] ?? ''},
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {
      await _logService.addLog('DEBUG', 'TradingController', '앱 상태 확인 실패: $e');
    }

    return null;
  }

  // 설정 업데이트
  void updateConfig(Map<String, dynamic> config) {
    _appConfig = {..._appConfig, ...config};
    notifyListeners();
  }

  @override
  void dispose() {
    _wakeupTimer?.cancel();
    _sleepTimer?.cancel();
    super.dispose();
  }
}
