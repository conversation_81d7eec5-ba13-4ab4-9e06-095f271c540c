import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TestSignalService extends ChangeNotifier {
  static final TestSignalService _instance = TestSignalService._internal();
  factory TestSignalService() => _instance;
  TestSignalService._internal();

  Timer? _timer;
  bool _isRunning = false;
  final Random _random = Random();
  int _signalCount = 0;

  // 바이비트 무기한 선물 코인 목록 (실제 거래 가능한 코인들)
  final List<String> _coins = [
    'BTCUSDT',
    'ETHUSDT',
    'ADAUSDT',
    'DOTUSDT',
    'LINKUSDT',
    'LTCUSDT',
    'BNBUSDT',
    'XRPUSDT',
    'SOLUSDT',
    'AVAXUSDT',
    'MATICUSDT',
    'ATOMUSDT',
    'UNIUSDT',
    'FILUSDT',
    'AAVEUSDT',
    'SUSHIUSDT',
    'COMPUSDT',
    'MKRUSDT',
    'YFIUSDT',
    'SNXUSDT',
    'DOGEUSDT',
    'SHIBUSDT',
    'AXSUSDT',
    'SANDUSDT',
    'MANAUSDT',
    'ENJUSDT',
    'CHZUSDT',
    'ALPHAUSDT',
    'NEARUSDT',
    'FTMUSDT',
    'ONEUSDT',
    'HARMONYUSDT',
    'ZILUSDT',
    'VETUSDT',
    'ICXUSDT',
    'ONTUSDT',
    'QTUMUSDT',
    'ZECUSDT',
    'BATUSDT',
    'IOTAUSDT',
    'XLMUSDT',
    'TRXUSDT',
    'EOSUSDT',
    'NEOUSDT',
    'DASHUSDT',
    'ETCUSDT',
    'XMRUSDT',
    'ZRXUSDT',
    'OMGUSDT',
    'LSKUSDT',
  ];

  final List<String> _signalTypes = ['BUY', 'SELL'];
  final List<String> _recentSignals = [];

  bool get isRunning => _isRunning;
  int get signalCount => _signalCount;
  List<String> get recentSignals => List.unmodifiable(_recentSignals);

  Future<void> startTestSignals() async {
    if (_isRunning) return;

    _isRunning = true;
    _signalCount = 0;
    _recentSignals.clear();
    await _saveState();
    notifyListeners();

    // print('=== 웹앱 내부 신호 테스트 생성기 시작 ===');
    // print('5초마다 더미 신호를 생성합니다.');

    // 5초마다 신호 생성
    _timer = Timer.periodic(Duration(seconds: 5), (timer) async {
      await _generateTestSignal();
    });
  }

  Future<void> stopTestSignals() async {
    if (!_isRunning) return;

    _timer?.cancel();
    _timer = null;
    _isRunning = false;
    await _saveState();
    notifyListeners();

    print('=== 웹앱 내부 신호 테스트 생성기 중지 ===');
  }

  Future<void> _generateTestSignal() async {
    try {
      final coin = _coins[_random.nextInt(_coins.length)];
      final signalType = _signalTypes[_random.nextInt(_signalTypes.length)];
      final timestamp = DateTime.now();

      // 코인별 가격 범위 설정
      double price;
      if (coin.startsWith('BTC')) {
        price = 40000 + _random.nextDouble() * 30000; // 40k-70k
      } else if (coin.startsWith('ETH')) {
        price = 2000 + _random.nextDouble() * 2000; // 2k-4k
      } else if (coin.contains('DOGE') || coin.contains('SHIB')) {
        price = 0.00001 + _random.nextDouble() * 0.1; // 소액 코인
      } else {
        price = 0.1 + _random.nextDouble() * 100; // 일반 코인
      }

      final signalData = {
        'timestamp': timestamp.toIso8601String(),
        'coin': coin,
        'signal': signalType,
        'price': price.toStringAsFixed(6),
        'source': 'WEB_TEST_GENERATOR',
        'id': '${timestamp.millisecondsSinceEpoch}_${coin}_$signalType',
      };

      // LocalStorage에 로그 저장
      await _writeToLogFile(signalData);

      // 최근 신호 목록 업데이트 (최대 10개)
      final signalText =
          '${timestamp.toString().substring(11, 19)} | $coin | $signalType | \$${price.toStringAsFixed(6)}';
      _recentSignals.insert(0, signalText);
      if (_recentSignals.length > 10) {
        _recentSignals.removeLast();
      }

      _signalCount++;
      await _saveState();
      notifyListeners();

      // 신호 감지 서비스에 알림
      await _notifySignalDetectionService(signalData);

      print('테스트 신호 생성: $coin $signalType @ \$${price.toStringAsFixed(6)}');
    } catch (e) {
      debugPrint('테스트 신호 생성 오류: $e');
    }
  }

  Future<void> _writeToLogFile(Map<String, dynamic> signalData) async {
    try {
      // 로그 엔트리 생성 (기존 test_signal_generator.dart와 동일한 형식)
      final logEntry =
          '${signalData['timestamp']} | ${signalData['coin']} | ${signalData['signal']} | \$${signalData['price']} | ${signalData['source']}\n';

      // LocalStorage에 로그 누적 저장
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getString('capture_log') ?? '';
      final updatedLogs = existingLogs + logEntry;

      // 최근 100개 로그만 유지 (메모리 관리)
      final lines =
          updatedLogs.split('\n').where((line) => line.isNotEmpty).toList();
      if (lines.length > 100) {
        final recentLines = lines.skip(lines.length - 100).toList();
        await prefs.setString('capture_log', recentLines.join('\n') + '\n');
      } else {
        await prefs.setString('capture_log', updatedLogs);
      }
    } catch (e) {
      print('로그 파일 쓰기 오류: $e');
    }
  }

  Future<void> _notifySignalDetectionService(
    Map<String, dynamic> signalData,
  ) async {
    try {
      // 웹 환경에서만 CustomEvent 사용
      if (kIsWeb) {
        // CustomEvent를 통해 신호 감지 서비스에 알림
        // html.CustomEvent('testSignalGenerated', detail: signalData);
        // html.window.dispatchEvent(event);
        print('Test signal generated for web: $signalData');
      }
    } catch (e) {
      print('신호 감지 서비스 알림 오류: $e');
    }
  }

  Future<void> _saveState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('test_signal_running', _isRunning);
      await prefs.setInt('test_signal_count', _signalCount);
    } catch (e) {
      print('상태 저장 오류: $e');
    }
  }

  Future<void> loadState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isRunning = prefs.getBool('test_signal_running') ?? false;
      _signalCount = prefs.getInt('test_signal_count') ?? 0;

      // 앱 재시작 시 자동으로 테스트 신호 재개하지 않음
      _isRunning = false;
      await _saveState();
      notifyListeners();
    } catch (e) {
      print('상태 로드 오류: $e');
    }
  }

  Future<String> getLogContent() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('capture_log') ?? '';
    } catch (e) {
      print('로그 내용 읽기 오류: $e');
      return '';
    }
  }

  Future<void> clearLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('capture_log');
      _recentSignals.clear();
      notifyListeners();
      print('로그 내용이 삭제되었습니다.');
    } catch (e) {
      print('로그 삭제 오류: $e');
    }
  }

  // 수동으로 단일 신호 생성
  Future<void> generateSingleSignal() async {
    if (!_isRunning) {
      await _generateTestSignal();
    }
  }

  // 실제 로그 파일 모니터링 시작
  Future<void> startRealSignalMonitoring() async {
    if (_isRunning) return;

    _isRunning = true;
    _signalCount = 0;
    notifyListeners();

    // print('=== 실제 신호 로그 파일 모니터링 시작 ===');

    _timer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      await _checkRealLogFile();
    });
  }

  // 실제 로그 파일 체크
  Future<void> _checkRealLogFile() async {
    try {
      // 웹 환경에서는 직접 파일을 읽을 수 없으므로
      // SharedPreferences를 통해 실제 로그 내용을 시뮬레이션
      final prefs = await SharedPreferences.getInstance();
      
      // 실제 환경에서는 외부 프로세스(예: 캡처 프로그램)가 이 값을 업데이트
      final realLogContent = prefs.getString('real_capture_log') ?? '';
      final lastProcessedPosition = prefs.getInt('last_processed_position') ?? 0;
      
      if (realLogContent.length > lastProcessedPosition) {
        // 새로운 로그 라인들 처리
        final newContent = realLogContent.substring(lastProcessedPosition);
        final newLines = newContent.split('\n').where((line) => line.trim().isNotEmpty).toList();
        
        for (final line in newLines) {
          await _processRealLogLine(line);
        }
        
        // 처리된 위치 업데이트
        await prefs.setInt('last_processed_position', realLogContent.length);
      }
    } catch (e) {
      print('Error checking real log file: $e');
    }
  }

  // 실제 로그 라인 처리
  Future<void> _processRealLogLine(String logLine) async {
    try {
      // 로그 라인 파싱 (예: "2024-01-01T12:00:00 | BTCUSDT | BUY | $50000.00 | REAL_CAPTURE")
      final parts = logLine.split(' | ');
      if (parts.length >= 5) {
        final timestamp = parts[0];
        final coin = parts[1];
        final signal = parts[2];
        final price = parts[3].replaceAll('\$', '');
        final source = parts[4];

        final signalData = {
          'timestamp': timestamp,
          'coin': coin,
          'signal': signal,
          'price': price,
          'source': source,
          'id': '${DateTime.now().millisecondsSinceEpoch}_${coin}_$signal',
        };

        // 최근 신호 목록 업데이트
        final signalText = '${timestamp.substring(11, 19)} | $coin | $signal | $price';
        _recentSignals.insert(0, signalText);
        if (_recentSignals.length > 10) {
          _recentSignals.removeLast();
        }

        _signalCount++;
        await _saveState();
        notifyListeners();

        // 신호 감지 서비스에 알림
        await _notifySignalDetectionService(signalData);

        print('실제 신호 처리: $coin $signal @ $price');
      }
    } catch (e) {
      print('로그 라인 처리 오류: $e');
    }
  }
}