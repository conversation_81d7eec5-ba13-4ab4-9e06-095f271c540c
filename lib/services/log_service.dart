import '../models/log_entry.dart';
import 'database_service.dart';

class LogService {
  final DatabaseService _databaseService = DatabaseService();

  // 로그 추가
  Future<void> addLog(String level, String category, String message) async {
    final logEntry = LogEntry(
      id: 0,
      level: level,
      category: category,
      message: message,
      timestamp: DateTime.now(),
    );
    
    await _databaseService.insertLog(logEntry);
    
    // 콘솔에도 출력
    print('[$level] $category: $message');
  }

  // 로그 목록 가져오기
  Future<List<LogEntry>> getLogs({int limit = 100}) async {
    return await _databaseService.getLogs(limit: limit);
  }

  // 로그 삭제
  Future<void> clearLogs() async {
    await _databaseService.clearLogs();
  }

  // 특정 레벨의 로그만 가져오기
  Future<List<LogEntry>> getLogsByLevel(String level) async {
    return await _databaseService.getLogsByLevel(level);
  }

  // 특정 카테고리의 로그만 가져오기
  Future<List<LogEntry>> getLogsByCategory(String category) async {
    return await _databaseService.getLogsByCategory(category);
  }
}
