import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'push_service.dart';
import '../models/signal.dart';

class SignalDetectionService extends ChangeNotifier {
  final PushService _pushService;

  Timer? _monitoringTimer;
  bool _isMonitoring = false;
  String? _currentLogFile;
  String _lastSignalType = '';
  String _lastCoin = '';

  SignalDetectionService(this._pushService);

  bool get isMonitoring => _isMonitoring;
  String? get currentLogFile => _currentLogFile;
  String get lastSignalType => _lastSignalType;
  String get lastCoin => _lastCoin;

  Future<void> startMonitoring([String? logFilePath]) async {
    if (_isMonitoring) {
      await stopMonitoring();
    }

    // 지정된 로그 파일 경로 사용
    _currentLogFile = logFilePath ?? r'D:\augment-projects\25_06_11\RealtimeCaptureApp\capture_log.txt';
    _isMonitoring = true;

    // 로그 파일 모니터링 시작 (3초마다 체크)
    _monitoringTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _checkLogFile();
    });

    notifyListeners();
    debugPrint('신호 감지 모니터링 시작: $_currentLogFile');
  }

  Future<void> stopMonitoring() async {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    _isMonitoring = false;
    _currentLogFile = null;

    notifyListeners();
    debugPrint('신호 감지 모니터링 중지');
  }

  Future<void> _checkLogFile() async {
    if (_currentLogFile == null || !_isMonitoring) return;

    try {
      final file = File(_currentLogFile!);
      if (!await file.exists()) {
        debugPrint('로그 파일이 존재하지 않습니다: $_currentLogFile');
        return;
      }

      final content = await file.readAsString();
      final lines = content.split('\n');

      // 최근 라인들에서 신호 패턴 검색
      for (final line in lines.reversed.take(50)) {
        if (line.trim().isEmpty) continue;

        final signal = _parseSignalFromLine(line);
        if (signal != null) {
          await _processDetectedSignal(signal);
          break; // 가장 최근 신호만 처리
        }
      }
    } catch (e) {
      debugPrint('로그 파일 모니터링 오류: $e');
    }
  }

  Signal? _parseSignalFromLine(String line) {
    try {
      // 신호 패턴 예시: "2024-01-01 12:00:00 LONG BTCUSDT"
      final patterns = [
        RegExp(
          r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}).*?(LONG|SHORT)\s+(\w+USDT)',
          caseSensitive: false,
        ),
        RegExp(r'(LONG|SHORT)\s+(\w+USDT)', caseSensitive: false),
        RegExp(r'(\w+USDT)\s+(LONG|SHORT)', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(line);
        if (match != null) {
          String signalType = '';
          String coin = '';

          if (match.groupCount >= 3) {
            // 패턴 1: 날짜, 신호타입, 코인
            signalType = match.group(2)?.toUpperCase() ?? '';
            coin = match.group(3)?.toUpperCase() ?? '';
          } else if (match.groupCount >= 2) {
            // 패턴 2 또는 3
            final group1 = match.group(1)?.toUpperCase() ?? '';
            final group2 = match.group(2)?.toUpperCase() ?? '';

            if (['LONG', 'SHORT'].contains(group1)) {
              signalType = group1;
              coin = group2;
            } else if (['LONG', 'SHORT'].contains(group2)) {
              signalType = group2;
              coin = group1;
            }
          }

          if (signalType.isNotEmpty &&
              coin.isNotEmpty &&
              coin.endsWith('USDT')) {
            return Signal(
              id: DateTime.now().millisecondsSinceEpoch,
              signalType: signalType,
              targetApp: 'mobile',
              payload:
                  '{"type":"$signalType","coin":"$coin","timestamp":"${DateTime.now().toIso8601String()}"}',
              status: 'pending',
              createdAt: DateTime.now(),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('신호 파싱 오류: $e');
    }

    return null;
  }

  Future<void> _processDetectedSignal(Signal signal) async {
    try {
      // payload에서 코인 정보 추출
      final payloadData = signal.payload;
      String coin = '';

      try {
        // JSON 파싱해서 코인 정보 추출
        final payloadParts = payloadData.split('"coin":"');
        if (payloadParts.length > 1) {
          coin = payloadParts[1].split('"')[0];
        }
      } catch (e) {
        debugPrint('페이로드 파싱 오류: $e');
        return;
      }

      // 메모리 기반 실시간 처리 (DB 저장 없이 즉시 처리)
      final now = DateTime.now();
      
      // 상태 즉시 업데이트 (실시간)
      _lastSignalType = signal.signalType;
      _lastCoin = coin;

      // 즉시 푸시 알림 전송 (실시간)
      await _pushService.sendSignalNotification(
        signalType: signal.signalType,
        coinName: coin,
      );

      // UI 즉시 업데이트 (실시간)
      notifyListeners();

      debugPrint('🚀 실시간 신호 처리: ${signal.signalType} $coin at ${now.toString().substring(11, 19)}');
    } catch (e) {
      debugPrint('신호 처리 오류: $e');
    }
  }

  Future<void> sendManualSignal(String signalType, String coin) async {
    try {
      final signal = Signal(
        id: DateTime.now().millisecondsSinceEpoch,
        signalType: signalType.toUpperCase(),
        targetApp: 'mobile',
        payload:
            '{"type":"${signalType.toUpperCase()}","coin":"${coin.toUpperCase()}","timestamp":"${DateTime.now().toIso8601String()}"}',
        status: 'pending',
        createdAt: DateTime.now(),
      );

      await _processDetectedSignal(signal);
      debugPrint('수동 신호 전송 완료: $signalType $coin');
    } catch (e) {
      debugPrint('수동 신호 전송 오류: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> getSignalStats() async {
    try {
      // 실시간 메모리 기반 통계 (DB 조회 없이)
      return {
        'total_signals': 1, // 실시간 처리 중
        'long_signals': _lastSignalType == 'LONG' ? 1 : 0,
        'short_signals': _lastSignalType == 'SHORT' ? 1 : 0,
        'last_signal': _lastSignalType.isNotEmpty ? _lastSignalType : null,
        'last_coin': _lastCoin.isNotEmpty ? _lastCoin : null,
      };
    } catch (e) {
      debugPrint('신호 통계 조회 오류: $e');
      return {
        'total_signals': 0,
        'long_signals': 0,
        'short_signals': 0,
        'last_signal': null,
        'last_coin': null,
      };
    }
  }

  @override
  void dispose() {
    _monitoringTimer?.cancel();
    super.dispose();
  }
}
