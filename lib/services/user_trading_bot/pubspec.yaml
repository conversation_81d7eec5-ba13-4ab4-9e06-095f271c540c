name: user_trading_signal_app
description: "사용자용 트레이딩 신호 수신 모바일 앱 - 관리자 웹앱에서 보내는 실시간 트레이딩 신호를 수신하고 푸시 알림으로 사용자에게 알림을 제공합니다."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3
  
  # State Management
  provider: ^6.1.2
  
  # Networking & WebSocket
  web_socket_channel: ^3.0.1
  http: ^1.2.2
  
  # Local Storage
  shared_preferences: ^2.3.2
  flutter_secure_storage: ^9.2.2  # 🔒 API 키 등 민감한 정보 보안 저장
  
  # Push Notifications
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^18.0.1
  
  # Audio & Vibration
  audioplayers: ^6.1.0
  vibration: ^2.0.0
  
  # Toast Messages
  fluttertoast: ^8.2.8
  
  # Utils
  intl: ^0.19.0
  crypto: ^3.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
