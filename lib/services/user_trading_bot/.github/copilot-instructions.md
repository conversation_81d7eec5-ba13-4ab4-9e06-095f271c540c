# Copilot Instructions for User Trading Signal App

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
This is a Flutter mobile application for receiving trading signals from an admin web dashboard. The app focuses on real-time signal reception, push notifications, and user experience.

## Key Features
- Real-time trading signal reception via WebSocket
- Push notifications for immediate alerts
- Signal history and management
- Clean, intuitive mobile UI
- Support for Android and iOS platforms

## Architecture Guidelines
- Use Provider or Riverpod for state management
- Implement clean architecture with separate layers (data, domain, presentation)
- Use WebSocket for real-time communication with admin dashboard
- Implement proper error handling and offline support
- Follow Material Design 3 guidelines

## Code Style
- Follow Flutter/Dart conventions
- Use meaningful variable and function names
- Add comprehensive comments for complex logic
- Implement proper error handling with try-catch blocks
- Use async/await for asynchronous operations

## Dependencies to Consider
- `provider` or `riverpod` for state management
- `web_socket_channel` for WebSocket connections
- `firebase_messaging` for push notifications
- `shared_preferences` for local storage
- `flutter_local_notifications` for local notifications
- `http` for API calls

## File Structure
- `lib/models/` - Data models (Signal, User, etc.)
- `lib/services/` - Business logic and API services
- `lib/screens/` - UI screens and pages
- `lib/widgets/` - Reusable UI components
- `lib/utils/` - Utility functions and constants
