import 'package:flutter_test/flutter_test.dart';
import '../lib/services/websocket_service.dart';
import '../lib/models/trading_signal.dart';

void main() {
  group('신호 처리 테스트', () {
    late WebSocketService webSocketService;

    setUp(() {
      webSocketService = WebSocketService();
    });

    tearDown(() {
      webSocketService.dispose();
    });

    test('🧪 LONG 신호 시뮬레이션 테스트', () async {
      print('\n🚀 === LONG 신호 테스트 시작 ===');

      // 신호 스트림 리스너 설정
      webSocketService.signalStream.listen((signal) {
        print('📢 신호 수신됨: ${signal.signalType} ${signal.coin}');
        expect(signal.signalType, 'LONG');
        expect(signal.coin, 'BTCUSDT');
      });

      // LONG 신호 시뮬레이션
      webSocketService.simulateSignalReceived({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': 'LONG',
        'coin': 'BTCUSDT',
        'timestamp': DateTime.now().toIso8601String(),
        'source': 'test_simulation',
      });

      await Future.delayed(Duration(seconds: 1));
      print('✅ LONG 신호 테스트 완료\n');
    });

    test('🧪 SHORT 신호 시뮬레이션 테스트', () async {
      print('🚀 === SHORT 신호 테스트 시작 ===');

      // 신호 스트림 리스너 설정
      webSocketService.signalStream.listen((signal) {
        print('📢 신호 수신됨: ${signal.signalType} ${signal.coin}');
        expect(signal.signalType, 'SHORT');
        expect(signal.coin, 'ETHUSDT');
      });

      // SHORT 신호 시뮬레이션
      webSocketService.simulateSignalReceived({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': 'SHORT',
        'coin': 'ETHUSDT',
        'timestamp': DateTime.now().toIso8601String(),
        'source': 'test_simulation',
      });

      await Future.delayed(Duration(seconds: 1));
      print('✅ SHORT 신호 테스트 완료\n');
    });

    test('🧪 WebSocket 메시지 형식 테스트', () async {
      print('🚀 === WebSocket 메시지 형식 테스트 시작 ===');

      // 관리자 대시보드에서 오는 실제 형식의 메시지 시뮬레이션
      final testMessage = {
        'type': 'signal_notification',
        'signalType': 'LONG',
        'coinName': 'BTCUSDT',
        'timestamp': DateTime.now().toIso8601String(),
      };

      print('📨 테스트 메시지: $testMessage');

      // 신호 처리 확인
      webSocketService.signalStream.listen((signal) {
        print('✅ 파싱된 신호: ${signal.toJson()}');
        expect(signal.signalType, 'LONG');
        expect(signal.coin, 'BTCUSDT');
        expect(signal.source, 'admin_dashboard');
      });

      // WebSocket 메시지 시뮬레이션 (내부적으로 _onMessage 로직 사용)
      webSocketService.simulateSignalReceived({
        'id': testMessage['timestamp'],
        'type': testMessage['signalType'],
        'coin': testMessage['coinName'],
        'timestamp': testMessage['timestamp'],
        'source': 'admin_dashboard',
      });

      await Future.delayed(Duration(seconds: 1));
      print('✅ WebSocket 메시지 형식 테스트 완료\n');
    });
  });

  group('TradingSignal 모델 테스트', () {
    test('🧪 JSON 변환 테스트', () {
      print('🚀 === JSON 변환 테스트 시작 ===');

      final originalJson = {
        'id': '12345',
        'type': 'LONG',
        'coin': 'BTCUSDT',
        'timestamp': '2025-01-01T00:00:00.000Z',
        'source': 'test',
      };

      final signal = TradingSignal.fromJson(originalJson);
      final convertedJson = signal.toJson();

      print('📥 입력 JSON: $originalJson');
      print('📤 출력 JSON: $convertedJson');

      expect(signal.signalType, 'LONG');
      expect(signal.coin, 'BTCUSDT');
      expect(signal.source, 'test');
      expect(convertedJson['signalType'], 'LONG');

      print('✅ JSON 변환 테스트 완료\n');
    });

    test('🧪 신호 타입 표시 테스트', () {
      print('🚀 === 신호 타입 표시 테스트 시작 ===');

      final longSignal = TradingSignal(
        id: '1',
        signalType: 'LONG',
        coin: 'BTCUSDT',
        timestamp: DateTime.now(),
        source: 'test',
      );

      final shortSignal = TradingSignal(
        id: '2',
        signalType: 'SHORT',
        coin: 'ETHUSDT',
        timestamp: DateTime.now(),
        source: 'test',
      );

      print(
        '📈 LONG 신호 - 색상: ${longSignal.signalColor}, 아이콘: ${longSignal.signalIcon}, 표시: ${longSignal.displaySignalType}',
      );
      print(
        '📉 SHORT 신호 - 색상: ${shortSignal.signalColor}, 아이콘: ${shortSignal.signalIcon}, 표시: ${shortSignal.displaySignalType}',
      );

      expect(longSignal.signalColor, '#4CAF50');
      expect(longSignal.signalIcon, '📈');
      expect(longSignal.displaySignalType, '롱 포지션');

      expect(shortSignal.signalColor, '#F44336');
      expect(shortSignal.signalIcon, '📉');
      expect(shortSignal.displaySignalType, '숏 포지션');

      print('✅ 신호 타입 표시 테스트 완료\n');
    });
  });
}
