import 'package:flutter_test/flutter_test.dart';
import '../lib/models/trading_signal.dart';

void main() {
  group('통합 테스트 - 신호 처리 흐름', () {
    test('🧪 신호 모델 기본 기능 테스트', () async {
      print('\n🚀 === 신호 모델 기본 기능 테스트 시작 ===');

      // 1. LONG 신호 생성
      final longSignal = TradingSignal(
        id: 'test_long_001',
        signalType: 'LONG',
        coin: 'BTCUSDT',
        timestamp: DateTime.now(),
        source: 'test_admin',
      );

      print('📈 LONG 신호 생성됨:');
      print('  - ID: ${longSignal.id}');
      print('  - 타입: ${longSignal.signalType}');
      print('  - 코인: ${longSignal.coin}');
      print('  - 색상: ${longSignal.signalColor}');
      print('  - 아이콘: ${longSignal.signalIcon}');
      print('  - 표시명: ${longSignal.displaySignalType}');

      // 2. SHORT 신호 생성
      final shortSignal = TradingSignal(
        id: 'test_short_001',
        signalType: 'SHORT',
        coin: 'ETHUSDT',
        timestamp: DateTime.now(),
        source: 'test_admin',
      );

      print('\n📉 SHORT 신호 생성됨:');
      print('  - ID: ${shortSignal.id}');
      print('  - 타입: ${shortSignal.signalType}');
      print('  - 코인: ${shortSignal.coin}');
      print('  - 색상: ${shortSignal.signalColor}');
      print('  - 아이콘: ${shortSignal.signalIcon}');
      print('  - 표시명: ${shortSignal.displaySignalType}');

      // 3. JSON 변환 테스트
      final longJson = longSignal.toJson();
      final recreatedSignal = TradingSignal.fromJson(longJson);

      print('\n🔄 JSON 변환 테스트:');
      print('  - 원본 타입: ${longSignal.signalType}');
      print('  - 변환 후 타입: ${recreatedSignal.signalType}');
      print(
        '  - 일치 여부: ${longSignal.signalType == recreatedSignal.signalType}',
      );

      // 검증
      expect(longSignal.signalColor, '#4CAF50');
      expect(shortSignal.signalColor, '#F44336');
      expect(recreatedSignal.signalType, longSignal.signalType);
      expect(recreatedSignal.coin, longSignal.coin);

      print('\n✅ 신호 모델 기본 기능 테스트 완료');
    });

    test('🧪 관리자 대시보드 메시지 형식 시뮬레이션', () async {
      print('\n🚀 === 관리자 대시보드 메시지 형식 시뮬레이션 시작 ===');

      // 관리자 대시보드에서 전송되는 실제 메시지 형식 시뮬레이션
      final adminMessage = {
        'type': 'signal_notification',
        'signalType': 'LONG',
        'coinName': 'BTCUSDT',
        'timestamp': DateTime.now().toIso8601String(),
      };

      print('📨 관리자 메시지: $adminMessage');

      // WebSocket 서비스에서 처리하는 방식으로 변환
      final processedSignal = TradingSignal.fromJson({
        'id': adminMessage['timestamp'],
        'type': adminMessage['signalType'],
        'coin': adminMessage['coinName'],
        'timestamp': adminMessage['timestamp'],
        'source': 'admin_dashboard',
      });

      print('🔄 처리된 신호:');
      print('  - ID: ${processedSignal.id}');
      print('  - 타입: ${processedSignal.signalType}');
      print('  - 코인: ${processedSignal.coin}');
      print('  - 소스: ${processedSignal.source}');

      // 검증
      expect(processedSignal.signalType, 'LONG');
      expect(processedSignal.coin, 'BTCUSDT');
      expect(processedSignal.source, 'admin_dashboard');

      print('\n✅ 관리자 대시보드 메시지 형식 시뮬레이션 완료');
    });

    test('🧪 반대 신호 감지 로직 테스트', () async {
      print('\n🚀 === 반대 신호 감지 로직 테스트 시작 ===');

      // 반대 신호 감지 테스트
      final isOpposite1 = TradingSignal.isOppositeSignal('LONG', 'SHORT');
      final isOpposite2 = TradingSignal.isOppositeSignal('SHORT', 'LONG');
      final isNotOpposite1 = TradingSignal.isOppositeSignal('LONG', 'LONG');
      final isNotOpposite2 = TradingSignal.isOppositeSignal('SHORT', 'SHORT');

      print('🔍 반대 신호 감지 테스트:');
      print('  - LONG → SHORT: $isOpposite1 (반대여야 함)');
      print('  - SHORT → LONG: $isOpposite2 (반대여야 함)');
      print('  - LONG → LONG: $isNotOpposite1 (같은 방향)');
      print('  - SHORT → SHORT: $isNotOpposite2 (같은 방향)');

      // 검증
      expect(isOpposite1, isTrue);
      expect(isOpposite2, isTrue);
      expect(isNotOpposite1, isFalse);
      expect(isNotOpposite2, isFalse);

      print('\n✅ 반대 신호 감지 로직 테스트 완료');
    });

    test('🧪 다양한 코인 지원 테스트', () async {
      print('\n🚀 === 다양한 코인 지원 테스트 시작 ===');

      final supportedCoins = [
        'BTCUSDT',
        'ETHUSDT',
        'ADAUSDT',
        'SOLUSDT',
        'XRPUSDT',
        'DOGEUSDT',
      ];

      for (final coin in supportedCoins) {
        final signal = TradingSignal(
          id: 'test_${coin}_${DateTime.now().millisecondsSinceEpoch}',
          signalType: 'LONG',
          coin: coin,
          timestamp: DateTime.now(),
          source: 'test',
        );

        print(
          '💰 $coin 신호 처리: ${signal.signalIcon} ${signal.displaySignalType}',
        );

        // 기본 검증
        expect(signal.coin, coin);
        expect(signal.signalType, 'LONG');
        expect(signal.signalColor, '#4CAF50');
      }

      print('\n✅ 다양한 코인 지원 테스트 완료');
    });
  });

  group('보안 테스트', () {
    test('🔒 테스트넷 금지 확인', () async {
      print('\n🚀 === 테스트넷 금지 확인 테스트 시작 ===');

      // 신호에 테스트넷 관련 키워드가 없는지 확인
      final signal = TradingSignal(
        id: 'security_test',
        signalType: 'LONG',
        coin: 'BTCUSDT',
        timestamp: DateTime.now(),
        source: 'security_test',
      );

      final signalJson = signal.toJson();
      final jsonString = signalJson.toString().toLowerCase();

      print('🔍 보안 검사:');
      print('  - 테스트넷 키워드 포함 여부: ${jsonString.contains('testnet')}');
      print('  - 지표 키워드 포함 여부: ${jsonString.contains('indicator')}');

      // 보안 검증
      expect(jsonString.contains('testnet'), isFalse);
      expect(jsonString.contains('indicator'), isFalse);

      print('\n✅ 보안 테스트 완료 - 테스트넷 및 지표 키워드 없음');
    });
  });
}
