import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/trading_signal.dart';

class WebSocketService extends ChangeNotifier {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  WebSocketChannel? _channel;
  bool _isConnected = false;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  String? _serverUrl;

  // 신호 스트림
  final _signalController = StreamController<TradingSignal>.broadcast();
  Stream<TradingSignal> get signalStream => _signalController.stream;

  // 연결 상태 스트림
  final _connectionController = StreamController<bool>.broadcast();
  Stream<bool> get connectionStream => _connectionController.stream;

  bool get isConnected => _isConnected;

  Future<void> connect(String serverUrl) async {
    try {
      _serverUrl = serverUrl;

      // 기존 연결 종료
      await disconnect();

      debugPrint('🔗 WebSocket 연결 시도: $serverUrl');

      _channel = WebSocketChannel.connect(Uri.parse(serverUrl));

      // 연결 리스너 설정
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _isConnected = true;
      _connectionController.add(true);
      notifyListeners();

      // 하트비트 시작
      _startHeartbeat();

      debugPrint('✅ WebSocket 연결 성공');
    } catch (e) {
      debugPrint('❌ WebSocket 연결 실패: $e');
      _onError(e);
    }
  }

  void _onMessage(dynamic message) {
    try {
      debugPrint('📨 WebSocket 메시지 수신: $message');

      final data = jsonDecode(message);

      // 신호 데이터 처리
      if (data['type'] == 'signal_notification') {
        final signal = TradingSignal.fromJson({
          'id':
              data['timestamp'] ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          'type': data['signalType'] ?? '',
          'coin': data['coinName'] ?? '',
          'timestamp': data['timestamp'] ?? DateTime.now().toIso8601String(),
          'source': 'admin_dashboard',
        });

        _signalController.add(signal);
        debugPrint('🚀 신호 수신: ${signal.signalType} ${signal.coin}');

        // 자동 신호 처리 실행 (보안 저장소 연동)
        TradingSignal.handleIncomingSignal(signal.signalType, signal.coin);
      }
      // 하트비트 응답 처리
      else if (data['type'] == 'pong') {
        debugPrint('💓 하트비트 응답 수신');
      }
    } catch (e) {
      debugPrint('📨 메시지 파싱 오류: $e');
    }
  }

  void _onError(error) {
    debugPrint('❌ WebSocket 오류: $error');
    _isConnected = false;
    _connectionController.add(false);
    notifyListeners();

    // 자동 재연결 시도
    _startReconnectTimer();
  }

  void _onDisconnected() {
    debugPrint('🔌 WebSocket 연결 종료');
    _isConnected = false;
    _connectionController.add(false);
    notifyListeners();

    // 자동 재연결 시도
    _startReconnectTimer();
  }

  void _startReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!_isConnected && _serverUrl != null) {
        debugPrint('🔄 WebSocket 재연결 시도...');
        connect(_serverUrl!);
      } else {
        timer.cancel();
      }
    });
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isConnected && _channel != null) {
        try {
          _channel!.sink.add(
            jsonEncode({
              'type': 'ping',
              'timestamp': DateTime.now().toIso8601String(),
            }),
          );
        } catch (e) {
          debugPrint('💓 하트비트 전송 실패: $e');
        }
      }
    });
  }

  Future<void> disconnect() async {
    debugPrint('🔌 WebSocket 연결 종료 중...');

    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();

    if (_channel != null) {
      await _channel!.sink.close(status.normalClosure);
      _channel = null;
    }

    _isConnected = false;
    _connectionController.add(false);
    notifyListeners();

    debugPrint('✅ WebSocket 연결 종료 완료');
  }

  /// 테스트용 신호 시뮬레이션 메서드
  /// 실제 WebSocket 연결 없이 신호를 직접 주입하여 테스트할 수 있습니다
  void simulateSignalReceived(Map<String, dynamic> signalData) {
    try {
      debugPrint('🧪 테스트 신호 시뮬레이션: $signalData');

      final signal = TradingSignal.fromJson(signalData);
      _signalController.add(signal);

      debugPrint('✅ 시뮬레이션 신호 처리 완료: ${signal.signalType} ${signal.coin}');
    } catch (e) {
      debugPrint('❌ 시뮬레이션 신호 처리 오류: $e');
    }
  }

  @override
  void dispose() {
    disconnect();
    _signalController.close();
    _connectionController.close();
    super.dispose();
  }
}
