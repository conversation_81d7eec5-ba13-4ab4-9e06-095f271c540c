import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:vibration/vibration.dart';
import '../models/trading_signal.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Android 설정
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS 설정
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 권한 요청
      await _requestPermissions();

      _isInitialized = true;
      debugPrint('✅ 알림 서비스 초기화 완료');
    } catch (e) {
      debugPrint('❌ 알림 서비스 초기화 실패: $e');
    }
  }

  Future<void> _requestPermissions() async {
    // Android 13+ 알림 권한 요청
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.requestNotificationsPermission();

    // iOS 권한 요청
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);
  }

  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('📱 알림 탭됨: ${response.payload}');
    // 알림 탭 시 앱으로 이동하는 로직 추가 가능
  }

  Future<void> showSignalNotification(TradingSignal signal) async {
    if (!_notificationsEnabled || !_isInitialized) return;

    try {
      final id = DateTime.now().millisecondsSinceEpoch.remainder(100000);

      // 알림 제목과 내용
      final title = '🚨 ${signal.displaySignalType} 신호';
      final body = '${signal.coin} - ${signal.signalIcon} ${signal.signalType}';

      // Android 알림 세부 설정
      final androidDetails = AndroidNotificationDetails(
        'trading_signals',
        '트레이딩 신호',
        channelDescription: '실시간 트레이딩 신호 알림',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: _vibrationEnabled,
        playSound: _soundEnabled,
        icon: '@mipmap/ic_launcher',
        ledOnMs: 1000,
        ledOffMs: 500,
      );

      // iOS 알림 세부 설정
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // 알림 표시
      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        details,
        payload: signal.id,
      );

      // 진동
      if (_vibrationEnabled) {
        await _playVibration();
      }

      // 사운드 재생
      if (_soundEnabled) {
        await _playNotificationSound(signal.signalType);
      }

      debugPrint('📱 신호 알림 전송: $title - $body');
    } catch (e) {
      debugPrint('❌ 알림 전송 실패: $e');
    }
  }

  Future<void> _playVibration() async {
    try {
      if (await Vibration.hasVibrator()) {
        // 패턴 진동: 500ms 진동, 200ms 휴식, 500ms 진동
        await Vibration.vibrate(
          pattern: [0, 500, 200, 500],
          intensities: [0, 255, 0, 255],
        );
      }
    } catch (e) {
      debugPrint('🔇 진동 재생 실패: $e');
    }
  }

  Future<void> _playNotificationSound(String signalType) async {
    try {
      // 기본 시스템 알림음 사용
      debugPrint('🔊 알림음 재생: $signalType');
    } catch (e) {
      debugPrint('🔇 사운드 재생 실패: $e');
    }
  }

  Future<void> showTestNotification() async {
    final testSignal = TradingSignal(
      id: 'test',
      signalType: 'LONG',
      coin: 'BTCUSDT',
      timestamp: DateTime.now(),
      source: 'test',
    );

    await showSignalNotification(testSignal);
  }

  // 설정 메소드들
  void setNotificationsEnabled(bool enabled) {
    _notificationsEnabled = enabled;
  }

  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  void setVibrationEnabled(bool enabled) {
    _vibrationEnabled = enabled;
  }

  bool get notificationsEnabled => _notificationsEnabled;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
}
