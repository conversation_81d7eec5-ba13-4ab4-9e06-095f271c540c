import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// 🔒 API 키와 민감한 정보를 안전하게 저장/관리하는 보안 저장소 서비스
///
/// 기능:
/// - API 키 암호화 저장
/// - 바이비트 데모/실거래 API 키 분리 관리
/// - 사용자 설정 보안 저장
/// - 자동 암호화/복호화
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true, // Android 암호화 강화
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      groupId: 'group.opensystems.trading.signal', // iOS Keychain 그룹
      accountName: 'TradingSignalApp',
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // 🔑 저장소 키 상수들
  static const String _keyBybitDemoApiKey = 'bybit_demo_api_key';
  static const String _keyBybitDemoSecretKey = 'bybit_demo_secret_key';
  static const String _keyBybitRealApiKey = 'bybit_real_api_key';
  static const String _keyBybitRealSecretKey = 'bybit_real_secret_key';
  static const String _keyUsername = 'username';
  static const String _keyTradingMode = 'trading_mode'; // 'demo' or 'real'
  static const String _keyApiBaseUrl = 'api_base_url';
  static const String _keyNotificationToken = 'notification_token';
  static const String _keyLastSignalTime = 'last_signal_time';

  // 🚫 바이비트 데모 API 키 저장 (테스트넷 아님 - 메인넷 데모)
  static Future<void> saveBybitDemoApiKeys({
    required String apiKey,
    required String secretKey,
  }) async {
    try {
      await _storage.write(
        key: _keyBybitDemoApiKey,
        value: _encryptData(apiKey),
      );
      await _storage.write(
        key: _keyBybitDemoSecretKey,
        value: _encryptData(secretKey),
      );
      print('[SECURE] 바이비트 데모 API 키 저장 완료');
    } catch (e) {
      print('[ERROR] 데모 API 키 저장 실패: $e');
      throw Exception('데모 API 키 저장에 실패했습니다: $e');
    }
  }

  // ✅ 바이비트 실거래 API 키 저장
  static Future<void> saveBybitRealApiKeys({
    required String apiKey,
    required String secretKey,
  }) async {
    try {
      await _storage.write(
        key: _keyBybitRealApiKey,
        value: _encryptData(apiKey),
      );
      await _storage.write(
        key: _keyBybitRealSecretKey,
        value: _encryptData(secretKey),
      );
      print('[SECURE] 바이비트 실거래 API 키 저장 완료');
    } catch (e) {
      print('[ERROR] 실거래 API 키 저장 실패: $e');
      throw Exception('실거래 API 키 저장에 실패했습니다: $e');
    }
  }

  // 🔑 바이비트 API 키 조회 (모드에 따라 자동 선택)
  static Future<Map<String, String?>> getBybitApiKeys(String mode) async {
    try {
      String? apiKey, secretKey;

      if (mode.toLowerCase() == 'demo') {
        final encryptedApiKey = await _storage.read(key: _keyBybitDemoApiKey);
        final encryptedSecretKey = await _storage.read(
          key: _keyBybitDemoSecretKey,
        );
        apiKey = encryptedApiKey != null ? _decryptData(encryptedApiKey) : null;
        secretKey =
            encryptedSecretKey != null
                ? _decryptData(encryptedSecretKey)
                : null;
      } else if (mode.toLowerCase() == 'real') {
        final encryptedApiKey = await _storage.read(key: _keyBybitRealApiKey);
        final encryptedSecretKey = await _storage.read(
          key: _keyBybitRealSecretKey,
        );
        apiKey = encryptedApiKey != null ? _decryptData(encryptedApiKey) : null;
        secretKey =
            encryptedSecretKey != null
                ? _decryptData(encryptedSecretKey)
                : null;
      }

      return {'apiKey': apiKey, 'secretKey': secretKey};
    } catch (e) {
      print('[ERROR] API 키 조회 실패: $e');
      return {'apiKey': null, 'secretKey': null};
    }
  }

  // 📱 사용자 설정 저장
  static Future<void> saveUserSettings({
    required String username,
    required String tradingMode, // 'demo' or 'real'
    required String apiBaseUrl,
  }) async {
    try {
      await _storage.write(key: _keyUsername, value: username);
      await _storage.write(key: _keyTradingMode, value: tradingMode);
      await _storage.write(key: _keyApiBaseUrl, value: apiBaseUrl);
      print('[SECURE] 사용자 설정 저장 완료');
    } catch (e) {
      print('[ERROR] 사용자 설정 저장 실패: $e');
      throw Exception('사용자 설정 저장에 실패했습니다: $e');
    }
  }

  // 📱 사용자 설정 조회
  static Future<Map<String, String?>> getUserSettings() async {
    try {
      final username = await _storage.read(key: _keyUsername);
      final tradingMode = await _storage.read(key: _keyTradingMode);
      final apiBaseUrl = await _storage.read(key: _keyApiBaseUrl);

      return {
        'username': username,
        'tradingMode': tradingMode ?? 'demo', // 기본값은 데모
        'apiBaseUrl': apiBaseUrl,
      };
    } catch (e) {
      print('[ERROR] 사용자 설정 조회 실패: $e');
      return {'username': null, 'tradingMode': 'demo', 'apiBaseUrl': null};
    }
  }

  // 🔔 알림 토큰 저장
  static Future<void> saveNotificationToken(String token) async {
    try {
      await _storage.write(key: _keyNotificationToken, value: token);
      print('[SECURE] 알림 토큰 저장 완료');
    } catch (e) {
      print('[ERROR] 알림 토큰 저장 실패: $e');
    }
  }

  // 🔔 알림 토큰 조회
  static Future<String?> getNotificationToken() async {
    try {
      return await _storage.read(key: _keyNotificationToken);
    } catch (e) {
      print('[ERROR] 알림 토큰 조회 실패: $e');
      return null;
    }
  }

  // 🕒 마지막 신호 시간 저장
  static Future<void> saveLastSignalTime(DateTime timestamp) async {
    try {
      await _storage.write(
        key: _keyLastSignalTime,
        value: timestamp.toIso8601String(),
      );
    } catch (e) {
      print('[ERROR] 마지막 신호 시간 저장 실패: $e');
    }
  }

  // 🕒 마지막 신호 시간 조회
  static Future<DateTime?> getLastSignalTime() async {
    try {
      final timeStr = await _storage.read(key: _keyLastSignalTime);
      return timeStr != null ? DateTime.parse(timeStr) : null;
    } catch (e) {
      print('[ERROR] 마지막 신호 시간 조회 실패: $e');
      return null;
    }
  }

  // 🧹 모든 데이터 삭제 (로그아웃 시)
  static Future<void> clearAllData() async {
    try {
      await _storage.deleteAll();
      print('[SECURE] 모든 보안 데이터 삭제 완료');
    } catch (e) {
      print('[ERROR] 데이터 삭제 실패: $e');
    }
  }

  // 🔑 특정 API 키만 삭제
  static Future<void> clearApiKeys(String mode) async {
    try {
      if (mode.toLowerCase() == 'demo') {
        await _storage.delete(key: _keyBybitDemoApiKey);
        await _storage.delete(key: _keyBybitDemoSecretKey);
        print('[SECURE] 데모 API 키 삭제 완료');
      } else if (mode.toLowerCase() == 'real') {
        await _storage.delete(key: _keyBybitRealApiKey);
        await _storage.delete(key: _keyBybitRealSecretKey);
        print('[SECURE] 실거래 API 키 삭제 완료');
      }
    } catch (e) {
      print('[ERROR] API 키 삭제 실패: $e');
    }
  }

  // 🔐 데이터 암호화 (추가 보안 계층)
  static String _encryptData(String data) {
    final bytes = utf8.encode(data);
    final hash = sha256.convert(bytes);
    return base64.encode(bytes) + '.' + hash.toString().substring(0, 16);
  }

  // 🔓 데이터 복호화
  static String _decryptData(String encryptedData) {
    try {
      final parts = encryptedData.split('.');
      if (parts.length != 2) throw Exception('Invalid format');

      final data = base64.decode(parts[0]);
      final originalHash = parts[1];

      // 무결성 검증
      final hash = sha256.convert(data);
      if (hash.toString().substring(0, 16) != originalHash) {
        throw Exception('Data integrity check failed');
      }

      return utf8.decode(data);
    } catch (e) {
      print('[ERROR] 데이터 복호화 실패: $e');
      throw Exception('데이터 복호화에 실패했습니다');
    }
  }

  // 📊 저장소 상태 확인
  static Future<Map<String, bool>> getStorageStatus() async {
    try {
      final demoApiKey = await _storage.read(key: _keyBybitDemoApiKey);
      final realApiKey = await _storage.read(key: _keyBybitRealApiKey);
      final username = await _storage.read(key: _keyUsername);
      final notificationToken = await _storage.read(key: _keyNotificationToken);

      return {
        'hasDemoApiKey': demoApiKey != null,
        'hasRealApiKey': realApiKey != null,
        'hasUsername': username != null,
        'hasNotificationToken': notificationToken != null,
      };
    } catch (e) {
      print('[ERROR] 저장소 상태 확인 실패: $e');
      return {
        'hasDemoApiKey': false,
        'hasRealApiKey': false,
        'hasUsername': false,
        'hasNotificationToken': false,
      };
    }
  }

  // 🌐 바이비트 API 엔드포인트 반환 (모드에 따라 자동 선택)
  static String getBybitApiEndpoint(String mode) {
    switch (mode.toLowerCase()) {
      case 'demo':
        return 'https://api-demo.bybit.com'; // 🚫 테스트넷 아님 - 메인넷 데모
      case 'real':
        return 'https://api.bybit.com'; // ✅ 실거래
      default:
        throw Exception('🚫 지원하지 않는 모드: $mode (demo 또는 real만 허용)');
    }
  }

  // 📡 WebSocket 엔드포인트 반환
  static String getBybitWebSocketEndpoint(String mode) {
    switch (mode.toLowerCase()) {
      case 'demo':
        return 'wss://stream-demo.bybit.com'; // 데모용 WebSocket
      case 'real':
        return 'wss://stream.bybit.com'; // 실거래용 WebSocket
      default:
        throw Exception('🚫 지원하지 않는 모드: $mode (demo 또는 real만 허용)');
    }
  }
}
