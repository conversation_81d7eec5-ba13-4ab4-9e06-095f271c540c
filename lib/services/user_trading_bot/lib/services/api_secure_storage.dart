import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// 🔒 API 키와 중요한 설정 정보를 안전하게 저장하는 보안 저장소 서비스
/// 데모/실거래 환경별로 별도 저장하여 혼동 방지
class ApiSecureStorage {
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      preferencesKeyPrefix: 'trading_bot_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.trading.signal.app',
      accountName: 'TradingSignalApp',
    ),
  );  // 🚫 테스트넷 절대 금지 - 데모/실거래만 허용
  // ignore: constant_identifier_names
  static const String DEMO_PREFIX = 'DEMO_';
  // ignore: constant_identifier_names
  static const String REAL_PREFIX = 'REAL_';

  // 저장 키 상수
  // ignore: constant_identifier_names
  static const String API_KEY = 'api_key';
  // ignore: constant_identifier_names
  static const String API_SECRET = 'api_secret';
  // ignore: constant_identifier_names
  static const String BASE_URL = 'base_url';
  // ignore: constant_identifier_names
  static const String USERNAME = 'username';
  // ignore: constant_identifier_names
  static const String TRADING_MODE = 'trading_mode'; // 'demo' or 'real'
  // ignore: constant_identifier_names
  static const String LAST_SELECTED_MODE = 'last_selected_mode';

  /// 🔐 데모 환경 API 키 저장
  static Future<void> saveDemoApiKey(String apiKey) async {
    await _storage.write(key: '$DEMO_PREFIX$API_KEY', value: _encrypt(apiKey));
  }

  /// 🔐 실거래 환경 API 키 저장
  static Future<void> saveRealApiKey(String apiKey) async {
    await _storage.write(key: '$REAL_PREFIX$API_KEY', value: _encrypt(apiKey));
  }

  /// 🔐 데모 환경 API 시크릿 저장
  static Future<void> saveDemoApiSecret(String apiSecret) async {
    await _storage.write(
      key: '$DEMO_PREFIX$API_SECRET',
      value: _encrypt(apiSecret),
    );
  }

  /// 🔐 실거래 환경 API 시크릿 저장
  static Future<void> saveRealApiSecret(String apiSecret) async {
    await _storage.write(
      key: '$REAL_PREFIX$API_SECRET',
      value: _encrypt(apiSecret),
    );
  }

  /// 📋 데모 환경 API 키 불러오기
  static Future<String?> getDemoApiKey() async {
    final encrypted = await _storage.read(key: '$DEMO_PREFIX$API_KEY');
    return encrypted != null ? _decrypt(encrypted) : null;
  }

  /// 📋 실거래 환경 API 키 불러오기
  static Future<String?> getRealApiKey() async {
    final encrypted = await _storage.read(key: '$REAL_PREFIX$API_KEY');
    return encrypted != null ? _decrypt(encrypted) : null;
  }

  /// 📋 데모 환경 API 시크릿 불러오기
  static Future<String?> getDemoApiSecret() async {
    final encrypted = await _storage.read(key: '$DEMO_PREFIX$API_SECRET');
    return encrypted != null ? _decrypt(encrypted) : null;
  }

  /// 📋 실거래 환경 API 시크릿 불러오기
  static Future<String?> getRealApiSecret() async {
    final encrypted = await _storage.read(key: '$REAL_PREFIX$API_SECRET');
    return encrypted != null ? _decrypt(encrypted) : null;
  }

  /// 🌐 데모 환경 Base URL 저장
  static Future<void> saveDemoBaseUrl(String baseUrl) async {
    await _storage.write(key: '$DEMO_PREFIX$BASE_URL', value: baseUrl);
  }

  /// 🌐 실거래 환경 Base URL 저장
  static Future<void> saveRealBaseUrl(String baseUrl) async {
    await _storage.write(key: '$REAL_PREFIX$BASE_URL', value: baseUrl);
  }

  /// 📋 데모 환경 Base URL 불러오기
  static Future<String?> getDemoBaseUrl() async {
    return await _storage.read(key: '$DEMO_PREFIX$BASE_URL');
  }

  /// 📋 실거래 환경 Base URL 불러오기
  static Future<String?> getRealBaseUrl() async {
    return await _storage.read(key: '$REAL_PREFIX$BASE_URL');
  }

  /// 👤 사용자명 저장
  static Future<void> saveUsername(String username) async {
    await _storage.write(key: USERNAME, value: username);
  }

  /// 📋 사용자명 불러오기
  static Future<String?> getUsername() async {
    return await _storage.read(key: USERNAME);
  }

  /// 📊 현재 선택된 거래 모드 저장 ('demo' or 'real')
  static Future<void> saveTradingMode(String mode) async {
    if (mode != 'demo' && mode != 'real') {
      throw ArgumentError('🚫 허용되지 않는 모드: $mode (demo 또는 real만 허용)');
    }
    await _storage.write(key: TRADING_MODE, value: mode);
    await _storage.write(key: LAST_SELECTED_MODE, value: mode);
  }

  /// 📋 현재 선택된 거래 모드 불러오기
  static Future<String> getTradingMode() async {
    final mode = await _storage.read(key: TRADING_MODE);
    return mode ?? 'demo'; // 기본값은 데모
  }

  /// 📋 마지막 선택된 모드 불러오기
  static Future<String> getLastSelectedMode() async {
    final mode = await _storage.read(key: LAST_SELECTED_MODE);
    return mode ?? 'demo';
  }

  /// 🔄 현재 모드에 따른 API 설정 정보 가져오기
  static Future<Map<String, String?>> getCurrentModeApiConfig() async {
    final mode = await getTradingMode();

    if (mode == 'demo') {
      return {
        'mode': 'demo',
        'apiKey': await getDemoApiKey(),
        'apiSecret': await getDemoApiSecret(),
        'baseUrl': await getDemoBaseUrl() ?? 'https://api-demo.bybit.com',
        'wsUrl': 'wss://stream-demo.bybit.com',
      };
    } else {
      return {
        'mode': 'real',
        'apiKey': await getRealApiKey(),
        'apiSecret': await getRealApiSecret(),
        'baseUrl': await getRealBaseUrl() ?? 'https://api.bybit.com',
        'wsUrl': 'wss://stream.bybit.com',
      };
    }
  }

  /// 🚫 특정 환경의 모든 API 정보 삭제
  static Future<void> clearModeData(String mode) async {
    if (mode == 'demo') {
      await _storage.delete(key: '$DEMO_PREFIX$API_KEY');
      await _storage.delete(key: '$DEMO_PREFIX$API_SECRET');
      await _storage.delete(key: '$DEMO_PREFIX$BASE_URL');
    } else if (mode == 'real') {
      await _storage.delete(key: '$REAL_PREFIX$API_KEY');
      await _storage.delete(key: '$REAL_PREFIX$API_SECRET');
      await _storage.delete(key: '$REAL_PREFIX$BASE_URL');
    }
  }

  /// 🗑️ 모든 저장된 데이터 삭제 (앱 초기화)
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  /// ✅ 현재 모드의 API 설정이 완료되었는지 확인
  static Future<bool> isCurrentModeConfigured() async {
    final config = await getCurrentModeApiConfig();
    return config['apiKey'] != null &&
        config['apiSecret'] != null &&
        config['baseUrl'] != null;
  }

  /// 🔐 간단한 암호화 (민감한 정보 보호)
  static String _encrypt(String value) {
    final bytes = utf8.encode(value + '_salt_trading_bot');
    final digest = sha256.convert(bytes);
    final encrypted = base64.encode(utf8.encode(value));
    return '$encrypted.${digest.toString().substring(0, 8)}';
  }

  /// 🔓 간단한 복호화
  static String _decrypt(String encryptedValue) {
    try {
      final parts = encryptedValue.split('.');
      if (parts.length != 2) return '';

      final decrypted = utf8.decode(base64.decode(parts[0]));
      return decrypted;
    } catch (e) {
      print('[ERROR] 복호화 실패: $e');
      return '';
    }
  }

  /// 📊 저장된 설정 정보 디버그 출력 (민감한 정보는 마스킹)
  static Future<void> debugPrintConfig() async {
    final demoConfig = {
      'apiKey': (await getDemoApiKey())?.replaceRange(8, null, '***'),
      'baseUrl': await getDemoBaseUrl(),
    };

    final realConfig = {
      'apiKey': (await getRealApiKey())?.replaceRange(8, null, '***'),
      'baseUrl': await getRealBaseUrl(),
    };

    final currentMode = await getTradingMode();

    print('[DEBUG] 📊 API 설정 상태:');
    print('  현재 모드: $currentMode');
    print('  데모 설정: $demoConfig');
    print('  실거래 설정: $realConfig');
  }
}
