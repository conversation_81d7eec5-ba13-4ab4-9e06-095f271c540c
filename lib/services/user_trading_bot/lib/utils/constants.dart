import 'dart:convert';
import 'package:flutter/material.dart';

/// Constants for the trading signal app
class AppConstants {
  // WebSocket configuration
  static const String defaultWebSocketUrl = 'ws://localhost:8080/signals';
  static const int reconnectDelaySeconds = 5;
  static const int maxReconnectAttempts = 10;
  static const int heartbeatIntervalSeconds = 30;

  // UI constants
  static const double cardBorderRadius = 12.0;
  static const double buttonBorderRadius = 8.0;
  static const double defaultPadding = 16.0;

  // Animation durations
  static const Duration cardAnimationDuration = Duration(milliseconds: 300);
  static const Duration fadeAnimationDuration = Duration(milliseconds: 200);

  // Signal types
  static const String signalTypeLong = 'LONG';
  static const String signalTypeShort = 'SHORT';

  // Supported coins (must match admin app)
  static const List<String> supportedCoins = [
    'BTCUSDT',
    'ETHUSDT',
    'BNBUSDT',
    'ADAUSDT',
    'DOTUSDT',
    'SOLUSDT',
  ];

  // Test data for development
  static Map<String, dynamic> getTestSignal({String? type, String? coin}) {
    final now = DateTime.now();
    return {
      'id': '${now.millisecondsSinceEpoch}',
      'type': type ?? signalTypeLong,
      'coin': coin ?? supportedCoins.first,
      'timestamp': now.toIso8601String(),
      'source': 'test',
      'notes': '테스트 신호입니다',
    };
  }

  // Pretty print JSON for debugging
  static String prettyPrintJson(Map<String, dynamic> json) {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(json);
  }
}

/// Utility class for formatting
class FormatUtils {
  /// Format time to display format (HH:mm:ss)
  static String formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}:'
        '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// Format date to display format (MM/dd HH:mm)
  static String formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.day.toString().padLeft(2, '0')} '
        '${formatTime(dateTime)}';
  }

  /// Format price with appropriate decimal places
  static String formatPrice(double? price) {
    if (price == null) return '-';
    if (price >= 1000) {
      return price.toStringAsFixed(2);
    } else if (price >= 1) {
      return price.toStringAsFixed(4);
    } else {
      return price.toStringAsFixed(8);
    }
  }

  /// Get signal type color
  static Color getSignalTypeColor(String signalType) {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return const Color(0xFF4CAF50); // Green
      case 'SHORT':
        return const Color(0xFFF44336); // Red
      default:
        return const Color(0xFF757575); // Grey
    }
  }

  /// Get coin display name
  static String getCoinDisplayName(String coin) {
    // Remove USDT suffix for display
    if (coin.endsWith('USDT')) {
      return coin.substring(0, coin.length - 4);
    }
    return coin;
  }
}

/// Logger utility for debugging
class Logger {
  static const bool _isDebugMode = true; // Set to false for release

  static void d(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      print('🐛 DEBUG: $tagStr$message');
    }
  }

  static void i(String message, [String? tag]) {
    if (_isDebugMode) {
      final tagStr = tag != null ? '[$tag] ' : '';
      print('ℹ️ INFO: $tagStr$message');
    }
  }

  static void w(String message, [String? tag]) {
    final tagStr = tag != null ? '[$tag] ' : '';
    print('⚠️ WARNING: $tagStr$message');
  }

  static void e(String message, [String? tag]) {
    final tagStr = tag != null ? '[$tag] ' : '';
    print('❌ ERROR: $tagStr$message');
  }
}
