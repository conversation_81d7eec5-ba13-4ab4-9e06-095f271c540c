import 'dart:async';
import 'dart:math';
import '../utils/constants.dart';

/// Mock WebSocket server simulator for testing purposes
/// This simulates the admin dashboard sending signals to test the mobile app
class MockWebSocketServer {
  static final MockWebSocketServer _instance = MockWebSocketServer._internal();
  factory MockWebSocketServer() => _instance;
  MockWebSocketServer._internal();

  Timer? _simulationTimer;
  final Random _random = Random();
  bool _isRunning = false;

  /// Start simulating signal broadcasts
  void startSimulation({
    Duration interval = const Duration(seconds: 10),
    Function(Map<String, dynamic>)? onSignalGenerated,
  }) {
    if (_isRunning) return;

    _isRunning = true;
    _simulationTimer = Timer.periodic(interval, (timer) {
      final signal = _generateRandomSignal();
      onSignalGenerated?.call(signal);
      Logger.i(
        '🎯 Mock signal generated: ${AppConstants.prettyPrintJson(signal)}',
        'MockServer',
      );
    });

    Logger.i('🚀 Mock WebSocket server simulation started', 'MockServer');
  }

  /// Stop signal simulation
  void stopSimulation() {
    _simulationTimer?.cancel();
    _simulationTimer = null;
    _isRunning = false;
    Logger.i('🛑 Mock WebSocket server simulation stopped', 'MockServer');
  }

  /// Generate a random trading signal for testing
  Map<String, dynamic> _generateRandomSignal() {
    final coinIndex = _random.nextInt(AppConstants.supportedCoins.length);
    final coin = AppConstants.supportedCoins[coinIndex];
    final isLong = _random.nextBool();
    final signalType =
        isLong ? AppConstants.signalTypeLong : AppConstants.signalTypeShort;

    // Generate realistic price ranges for different coins
    final priceRanges = {
      'BTCUSDT': {'min': 25000.0, 'max': 75000.0},
      'ETHUSDT': {'min': 1500.0, 'max': 4500.0},
      'BNBUSDT': {'min': 200.0, 'max': 600.0},
      'ADAUSDT': {'min': 0.2, 'max': 1.5},
      'DOTUSDT': {'min': 3.0, 'max': 30.0},
      'SOLUSDT': {'min': 10.0, 'max': 200.0},
    };

    final range = priceRanges[coin] ?? {'min': 1.0, 'max': 100.0};
    final basePrice =
        range['min']! + _random.nextDouble() * (range['max']! - range['min']!);

    // Generate target price (1-5% from base price)
    final targetMultiplier =
        isLong
            ? (1.01 + _random.nextDouble() * 0.04)
            : (0.95 + _random.nextDouble() * 0.04);
    final targetPrice = basePrice * targetMultiplier;

    // Generate stop loss (1-3% in opposite direction)
    final stopLossMultiplier =
        isLong
            ? (0.97 + _random.nextDouble() * 0.02)
            : (1.01 + _random.nextDouble() * 0.02);
    final stopLoss = basePrice * stopLossMultiplier;

    final notes = _generateRandomNotes(coin, signalType);

    return {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'type': signalType,
      'coin': coin,
      'timestamp': DateTime.now().toIso8601String(),
      'source': 'mock_admin',
      'targetPrice': targetPrice,
      'stopLoss': stopLoss,
      'notes': notes,
    };
  }

  /// Generate random notes for the signal
  String _generateRandomNotes(String coin, String signalType) {
    final coinName = FormatUtils.getCoinDisplayName(coin);
    final direction = signalType == AppConstants.signalTypeLong ? '상승' : '하락';

    final reasons = [
      '기술적 분석 기반 $direction 신호',
      '$coinName 강한 모멘텀 감지',
      '거래량 급증으로 인한 $direction 예상',
      '지지/저항선 돌파 확인',
      'RSI 과매수/과매도 구간 진입',
      '이동평균선 골든/데드 크로스',
      '볼린저 밴드 상/하단 터치',
      'MACD 신호 전환 확인',
    ];

    return reasons[_random.nextInt(reasons.length)];
  }

  /// Generate specific test signals for debugging
  Map<String, dynamic> generateTestSignal({
    required String coin,
    required String signalType,
    String? notes,
  }) {
    return AppConstants.getTestSignal(type: signalType, coin: coin)
      ..['notes'] = notes ?? '수동 테스트 신호';
  }

  bool get isRunning => _isRunning;
}

/// WebSocket connection test utilities
class WebSocketTestUtils {
  /// Test WebSocket connection with a given URL
  static Future<bool> testConnection(String url) async {
    try {
      Logger.i('🔍 Testing WebSocket connection: $url', 'TestUtils');

      // This is a simplified test - in a real scenario, you'd use WebSocketChannel
      // For now, we'll just validate the URL format
      final uri = Uri.parse(url);
      if (uri.scheme != 'ws' && uri.scheme != 'wss') {
        Logger.e('Invalid WebSocket scheme: ${uri.scheme}', 'TestUtils');
        return false;
      }

      Logger.i('✅ WebSocket URL format is valid', 'TestUtils');
      return true;
    } catch (e) {
      Logger.e('❌ WebSocket connection test failed: $e', 'TestUtils');
      return false;
    }
  }

  /// Generate sample signals for UI testing
  static List<Map<String, dynamic>> generateSampleSignals(int count) {
    final mockServer = MockWebSocketServer();
    final signals = <Map<String, dynamic>>[];

    for (int i = 0; i < count; i++) {
      signals.add(mockServer._generateRandomSignal());
      // Add some delay between signals for realistic timestamps
      if (i < count - 1) {
        Future.delayed(Duration(milliseconds: 100));
      }
    }

    return signals;
  }
}
