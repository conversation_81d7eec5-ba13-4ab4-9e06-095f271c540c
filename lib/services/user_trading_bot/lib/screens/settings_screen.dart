import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/websocket_service.dart';
import '../services/notification_service.dart';
import '../services/api_secure_storage.dart';
import '../utils/test_utils.dart';
import '../utils/constants.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _serverUrlController = TextEditingController();
  final _usernameController = TextEditingController();
  final _demoApiKeyController = TextEditingController();
  final _demoApiSecretController = TextEditingController();
  final _demoBaseUrlController = TextEditingController();
  final _realApiKeyController = TextEditingController();
  final _realApiSecretController = TextEditingController();
  final _realBaseUrlController = TextEditingController();

  late WebSocketService _webSocketService;
  late NotificationService _notificationService;

  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _isConnected = false;
  String _selectedMode = 'demo'; // 🚫 테스트넷 금지 - demo/real만 허용

  @override
  void initState() {
    super.initState();
    _webSocketService = WebSocketService();
    _notificationService = NotificationService();
    _loadSettings();
    _isConnected = _webSocketService.isConnected;

    _webSocketService.connectionStream.listen((connected) {
      if (mounted) {
        setState(() {
          _isConnected = connected;
        });
      }
    });
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 기존 설정 로드
    setState(() {
      _serverUrlController.text =
          prefs.getString('server_url') ?? 'ws://192.168.1.100:8080';
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
    });

    // 🔒 보안 저장소에서 API 설정 로드
    try {
      _selectedMode = await ApiSecureStorage.getTradingMode();
      final username = await ApiSecureStorage.getUsername();

      final demoApiKey = await ApiSecureStorage.getDemoApiKey();
      final demoApiSecret = await ApiSecureStorage.getDemoApiSecret();
      final demoBaseUrl = await ApiSecureStorage.getDemoBaseUrl();

      final realApiKey = await ApiSecureStorage.getRealApiKey();
      final realApiSecret = await ApiSecureStorage.getRealApiSecret();
      final realBaseUrl = await ApiSecureStorage.getRealBaseUrl();

      setState(() {
        _usernameController.text = username ?? '';
        _demoApiKeyController.text = demoApiKey ?? '';
        _demoApiSecretController.text = demoApiSecret ?? '';
        _demoBaseUrlController.text =
            demoBaseUrl ?? 'https://api-demo.bybit.com';
        _realApiKeyController.text = realApiKey ?? '';
        _realApiSecretController.text = realApiSecret ?? '';
        _realBaseUrlController.text = realBaseUrl ?? 'https://api.bybit.com';
      });
    } catch (e) {
      print('[ERROR] API 설정 로드 실패: $e');
    }
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // 기존 설정 저장
    await prefs.setString('server_url', _serverUrlController.text);
    await prefs.setBool('notifications_enabled', _notificationsEnabled);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);

    // 🔒 보안 저장소에 API 설정 저장
    try {
      await ApiSecureStorage.saveUsername(_usernameController.text);
      await ApiSecureStorage.saveTradingMode(_selectedMode);

      // 데모 환경 설정 저장
      if (_demoApiKeyController.text.isNotEmpty) {
        await ApiSecureStorage.saveDemoApiKey(_demoApiKeyController.text);
      }
      if (_demoApiSecretController.text.isNotEmpty) {
        await ApiSecureStorage.saveDemoApiSecret(_demoApiSecretController.text);
      }
      await ApiSecureStorage.saveDemoBaseUrl(_demoBaseUrlController.text);

      // 실거래 환경 설정 저장
      if (_realApiKeyController.text.isNotEmpty) {
        await ApiSecureStorage.saveRealApiKey(_realApiKeyController.text);
      }
      if (_realApiSecretController.text.isNotEmpty) {
        await ApiSecureStorage.saveRealApiSecret(_realApiSecretController.text);
      }
      await ApiSecureStorage.saveRealBaseUrl(_realBaseUrlController.text);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ 설정이 안전하게 저장되었습니다.'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('[ERROR] API 설정 저장 실패: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ 설정 저장 실패: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _connectWebSocket() async {
    try {
      await _webSocketService.connect(_serverUrlController.text);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ WebSocket 연결 성공'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ WebSocket 연결 실패: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _disconnectWebSocket() async {
    await _webSocketService.disconnect();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔌 WebSocket 연결 해제됨'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('⚙️ 설정'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
            tooltip: '설정 저장',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 🔒 사용자 정보 섹션
            _buildSectionCard(
              title: '👤 사용자 정보',
              icon: Icons.person,
              children: [
                _buildTextField(
                  controller: _usernameController,
                  label: '사용자명',
                  hint: '트레이딩 봇 사용자명을 입력하세요',
                  icon: Icons.account_circle,
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // 🚫 거래 모드 선택 (테스트넷 금지)
            _buildSectionCard(
              title: '📊 거래 모드 (🚫 테스트넷 금지)',
              icon: Icons.swap_horiz,
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: Colors.orange.shade300),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange.shade700),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          '🚫 테스트넷은 절대 금지됩니다. 데모/실거래만 허용됩니다.',
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12.h),
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('🧪 데모 모드'),
                        subtitle: const Text('가상 자금으로 안전하게 테스트'),
                        value: 'demo',
                        groupValue: _selectedMode,
                        onChanged: (value) {
                          setState(() {
                            _selectedMode = value!;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('💰 실거래 모드'),
                        subtitle: const Text('실제 자금으로 거래'),
                        value: 'real',
                        groupValue: _selectedMode,
                        onChanged: (value) {
                          setState(() {
                            _selectedMode = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // 🧪 데모 환경 API 설정
            _buildSectionCard(
              title: '🧪 데모 환경 API 설정',
              icon: Icons.science,
              children: [
                _buildTextField(
                  controller: _demoApiKeyController,
                  label: '데모 API 키',
                  hint: '데모 환경용 API 키를 입력하세요',
                  icon: Icons.vpn_key,
                  isPassword: true,
                ),
                SizedBox(height: 12.h),
                _buildTextField(
                  controller: _demoApiSecretController,
                  label: '데모 API 시크릿',
                  hint: '데모 환경용 API 시크릿을 입력하세요',
                  icon: Icons.security,
                  isPassword: true,
                ),
                SizedBox(height: 12.h),
                _buildTextField(
                  controller: _demoBaseUrlController,
                  label: '데모 Base URL',
                  hint: 'https://api-demo.bybit.com',
                  icon: Icons.link,
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // 💰 실거래 환경 API 설정
            _buildSectionCard(
              title: '💰 실거래 환경 API 설정',
              icon: Icons.monetization_on,
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: Colors.red.shade300),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.red.shade700),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          '⚠️ 실거래 환경 설정 시 신중하게 입력하세요. 실제 자금이 사용됩니다.',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12.h),
                _buildTextField(
                  controller: _realApiKeyController,
                  label: '실거래 API 키',
                  hint: '실거래 환경용 API 키를 입력하세요',
                  icon: Icons.vpn_key,
                  isPassword: true,
                ),
                SizedBox(height: 12.h),
                _buildTextField(
                  controller: _realApiSecretController,
                  label: '실거래 API 시크릿',
                  hint: '실거래 환경용 API 시크릿을 입력하세요',
                  icon: Icons.security,
                  isPassword: true,
                ),
                SizedBox(height: 12.h),
                _buildTextField(
                  controller: _realBaseUrlController,
                  label: '실거래 Base URL',
                  hint: 'https://api.bybit.com',
                  icon: Icons.link,
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // WebSocket 연결 설정
            _buildSectionCard(
              title: '🌐 WebSocket 연결',
              icon: Icons.wifi,
              children: [
                _buildTextField(
                  controller: _serverUrlController,
                  label: '서버 URL',
                  hint: 'ws://192.168.1.100:8080',
                  icon: Icons.cloud,
                ),
                SizedBox(height: 12.h),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isConnected ? null : _connectWebSocket,
                        icon: Icon(
                          _isConnected ? Icons.check_circle : Icons.wifi,
                        ),
                        label: Text(_isConnected ? '연결됨' : '연결'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              _isConnected ? Colors.green : Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isConnected ? _disconnectWebSocket : null,
                        icon: const Icon(Icons.wifi_off),
                        label: const Text('연결 해제'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // 알림 설정
            _buildSectionCard(
              title: '🔔 알림 설정',
              icon: Icons.notifications,
              children: [
                SwitchListTile(
                  title: const Text('푸시 알림'),
                  subtitle: const Text('신호 수신 시 푸시 알림을 받습니다'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                SwitchListTile(
                  title: const Text('알림 사운드'),
                  subtitle: const Text('신호 수신 시 사운드를 재생합니다'),
                  value: _soundEnabled,
                  onChanged: (value) {
                    setState(() {
                      _soundEnabled = value;
                    });
                  },
                ),
                SwitchListTile(
                  title: const Text('진동'),
                  subtitle: const Text('신호 수신 시 진동을 작동합니다'),
                  value: _vibrationEnabled,
                  onChanged: (value) {
                    setState(() {
                      _vibrationEnabled = value;
                    });
                  },
                ),
              ],
            ),

            SizedBox(height: 24.h),

            // 저장 버튼
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _saveSettings,
                icon: const Icon(Icons.save),
                label: const Text('설정 저장'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue.shade600),
                SizedBox(width: 8.w),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isPassword = false,
  }) {
    return TextField(
      controller: controller,
      obscureText: isPassword,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
    );
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    _usernameController.dispose();
    _demoApiKeyController.dispose();
    _demoApiSecretController.dispose();
    _demoBaseUrlController.dispose();
    _realApiKeyController.dispose();
    _realApiSecretController.dispose();
    _realBaseUrlController.dispose();
    super.dispose();
  }
}
