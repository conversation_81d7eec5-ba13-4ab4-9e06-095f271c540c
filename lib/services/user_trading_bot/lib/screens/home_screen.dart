import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../services/websocket_service.dart';
import '../services/notification_service.dart';
import '../models/trading_signal.dart';
import '../widgets/signal_card.dart';
import '../widgets/connection_status.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<TradingSignal> _signals = [];
  late WebSocketService _webSocketService;
  late NotificationService _notificationService;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    _webSocketService = WebSocketService();
    _notificationService = NotificationService();

    // 알림 서비스 초기화
    await _notificationService.initialize();

    // WebSocket 신호 스트림 리스닝
    _webSocketService.signalStream.listen((signal) {
      setState(() {
        _signals.insert(0, signal);
        // 최대 100개 신호만 유지
        if (_signals.length > 100) {
          _signals.removeLast();
        }
      });

      // 알림 표시
      _notificationService.showSignalNotification(signal);
    });

    // 자동 연결 (실제 환경에서는 설정에서 가져와야 함)
    // await _webSocketService.connect('ws://localhost:8080');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          '트레이딩 신호',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        actions: [
          // 연결 상태 표시
          ConnectionStatusWidget(),
          SizedBox(width: 8.w),

          // 설정 버튼
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
          SizedBox(width: 8.w),
        ],
      ),
      body: Column(
        children: [
          // 상단 통계 카드
          Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  '총 신호',
                  '${_signals.length}',
                  Colors.blue,
                  Icons.signal_cellular_alt,
                ),
                _buildStatItem(
                  '롱 신호',
                  '${_signals.where((s) => s.signalType.toUpperCase() == 'LONG').length}',
                  Colors.green,
                  Icons.trending_up,
                ),
                _buildStatItem(
                  '숏 신호',
                  '${_signals.where((s) => s.signalType.toUpperCase() == 'SHORT').length}',
                  Colors.red,
                  Icons.trending_down,
                ),
              ],
            ),
          ),

          // 신호 목록
          Expanded(
            child:
                _signals.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.signal_cellular_off,
                            size: 64.sp,
                            color: Colors.grey[400],
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            '아직 수신된 신호가 없습니다',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            '관리자가 신호를 보내면 여기에 표시됩니다',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      itemCount: _signals.length,
                      itemBuilder: (context, index) {
                        final signal = _signals[index];
                        return Padding(
                          padding: EdgeInsets.only(bottom: 12.h),
                          child: SignalCard(signal: signal),
                        );
                      },
                    ),
          ),
        ],
      ),

      // 하단 테스트 버튼 (개발용)
      floatingActionButton: FloatingActionButton(
        onPressed: _showTestDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: color, size: 24.sp),
        ),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12.sp, color: Colors.grey[600])),
      ],
    );
  }

  void _showTestDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('테스트'),
            content: const Text('테스트 기능을 선택하세요'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _addTestSignal();
                },
                child: const Text('테스트 신호 추가'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _notificationService.showTestNotification();
                },
                child: const Text('테스트 알림'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('취소'),
              ),
            ],
          ),
    );
  }

  void _addTestSignal() {
    final testSignal = TradingSignal(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      signalType: DateTime.now().second % 2 == 0 ? 'LONG' : 'SHORT',
      coin: ['BTCUSDT', 'ETHUSDT', 'XRPUSDT'][DateTime.now().second % 3],
      timestamp: DateTime.now(),
      source: 'test',
    );

    setState(() {
      _signals.insert(0, testSignal);
    });

    _notificationService.showSignalNotification(testSignal);
  }

  @override
  void dispose() {
    _webSocketService.dispose();
    super.dispose();
  }
}
