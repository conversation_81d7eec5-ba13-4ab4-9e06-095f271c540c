import 'dart:convert';
import 'package:http/http.dart' as http;
import '../services/api_secure_storage.dart';

class TradingSignal {
  final String id;
  final String signalType; // LONG or SHORT
  final String coin; // 코인 심볼 (예: BTCUSDT)
  final DateTime timestamp;
  final String source; // 신호 소스
  final double? targetPrice; // 목표가격 (선택사항)
  final double? stopLoss; // 손절가 (선택사항)
  final String? notes; // 추가 메모

  TradingSignal({
    required this.id,
    required this.signalType,
    required this.coin,
    required this.timestamp,
    required this.source,
    this.targetPrice,
    this.stopLoss,
    this.notes,
  });

  factory TradingSignal.fromJson(Map<String, dynamic> json) {
    return TradingSignal(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      signalType: json['type'] ?? json['signalType'] ?? '',
      coin: json['coin'] ?? '',
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'])
              : DateTime.now(),
      source: json['source'] ?? 'admin',
      targetPrice: json['targetPrice']?.toDouble(),
      stopLoss: json['stopLoss']?.toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'signalType': signalType,
      'coin': coin,
      'timestamp': timestamp.toIso8601String(),
      'source': source,
      'targetPrice': targetPrice,
      'stopLoss': stopLoss,
      'notes': notes,
    };
  }

  // 신호 타입에 따른 색상 반환
  String get signalColor {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '#4CAF50'; // 초록색
      case 'SHORT':
        return '#F44336'; // 빨간색
      default:
        return '#9E9E9E'; // 회색
    }
  }

  // 신호 타입에 따른 아이콘 반환
  String get signalIcon {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '📈'; // 상승 차트
      case 'SHORT':
        return '📉'; // 하락 차트
      default:
        return '📊'; // 기본 차트
    }
  }

  // 사용자 친화적인 신호 타입 반환
  String get displaySignalType {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '롱 포지션';
      case 'SHORT':
        return '숏 포지션';
      default:
        return signalType;
    }
  }

  // 신호 처리 메소드 - 신호 수신 시 자동으로 호출됨
  // 규칙: 신호로만 매매, 익절/손절 금지, 반대 신호 시 시장가 청산 후 지정가 진입
  static Future<void> handleIncomingSignal(
    String signalType,
    String coin,
  ) async {
    try {
      print('[INFO] 📢 $signalType 신호 수신: $coin');

      // 🔒 보안 저장소에서 현재 모드 및 API 설정 불러오기
      final apiConfig = await ApiSecureStorage.getCurrentModeApiConfig();
      final currentMode = apiConfig['mode'] ?? 'demo';
      final username = await ApiSecureStorage.getUsername() ?? 'unknown_user';
      final apiBaseUrl = apiConfig['baseUrl'] ?? 'http://localhost:3000';

      print('[INFO] 🔐 현재 거래 모드: $currentMode');
      print('[INFO] 🌐 API 서버: $apiBaseUrl');

      // 1. 사용자 알림
      playNotificationSound();
      showToast('📢 $signalType 신호 수신! ($coin) [${currentMode.toUpperCase()}]');

      // 2. 현재 포지션 확인
      final currentPosition = await getCurrentPosition(
        coin,
        username,
        apiBaseUrl,
      );

      // 3. 반대 신호인 경우 기존 포지션 시장가 청산
      if (currentPosition != null &&
          isOppositeSignal(currentPosition, signalType)) {
        updateBotStatus('🔄 반대 신호 감지 - 기존 $currentPosition 포지션 시장가 청산 중...');
        showToast('🔄 기존 $currentPosition 포지션 청산 중...');

        final closeSuccess = await closePositionAtMarket(
          coin,
          currentPosition,
          username,
          currentMode,
          apiBaseUrl,
        );
        if (closeSuccess) {
          await Future.delayed(Duration(seconds: 3)); // 청산 확인 대기
          updateBotStatus('✅ 기존 포지션 청산 완료 - 30초 후 새 포지션 진입');
          showToast('✅ $currentPosition 포지션 청산 완료');
        } else {
          updateBotStatus('❌ 포지션 청산 실패 - 수동 확인 필요');
          showToast('❌ 포지션 청산 실패');
          return; // 청산 실패 시 새 포지션 진입 중단
        }
      } else if (currentPosition == signalType.toUpperCase()) {
        // 같은 방향 신호는 무시
        updateBotStatus('⚠️ 동일한 $signalType 포지션 이미 보유 중 - 신호 무시');
        showToast('⚠️ 이미 $signalType 포지션 보유 중');
        return;
      } else {
        updateBotStatus(
          '📊 신호 수신됨 - 30초 후 지정가 진입 [${currentMode.toUpperCase()}]',
        );
      }

      // 4. 30초 대기 (시장 안정화 및 지정가 진입 준비)
      for (int i = 30; i > 0; i--) {
        updateBotStatus(
          '⏱️ $signalType 지정가 진입까지 ${i}초 대기... [${currentMode.toUpperCase()}]',
        );
        await Future.delayed(Duration(seconds: 1));
      }

      // 5. 지정가로 새 포지션 진입 (익절/손절 없음)
      updateBotStatus(
        '🎯 $signalType 지정가 진입 시도 중... [${currentMode.toUpperCase()}]',
      );

      // 🚫 테스트넷 절대 금지 - 데모/실거래만 허용하는 API 요청 구성
      final requestBody = {
        'username': username,
        'mode': currentMode, // � 보안 저장소에서 가져온 모드 ('demo' or 'real')
        'signal': signalType.toUpperCase(),
        'coin': coin,
        'entry': 'limit', // 지정가 진입
        'stopLoss': null, // 🚫 손절 금지 (지표 사용 금지)
        'takeProfit': null, // 🚫 익절 금지 (지표 사용 금지)
        'signalOnly': true, // ✅ 신호로만 매매 (지표 사용 금지)
        'useIndicators': false, // 🚫 지표 사용 절대 금지
        'useTestnet': false, // 🚫 테스트넷 사용 절대 금지
        'indicatorsDisabled': true, // 🚫 지표 비활성화 강제
        'testnetDisabled': true, // 🚫 테스트넷 비활성화 강제
        'demoRealOnly': true, // ✅ 데모/실거래만 허용
        'noTechnicalAnalysis': true, // 🚫 기술적 분석 사용 금지
        'modeValidated': currentMode, // ✅ 검증된 모드
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse('$apiBaseUrl/api/bot/start-signal-trade'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      // 6. 결과 반영
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        updateBotStatus(
          '✅ $signalType 지정가 포지션 진입 완료 [${currentMode.toUpperCase()}]',
        );
        showToast('🎯 $signalType 포지션 진입 성공! [${currentMode.toUpperCase()}]');
        print(
          '[SUCCESS] $signalType 포지션 진입 완료 [$currentMode]: ${responseData.toString()}',
        );
      } else {
        final errorMessage =
            response.body.isNotEmpty ? response.body : '알 수 없는 오류';
        updateBotStatus(
          '❌ 매매 실패: ${response.statusCode} [${currentMode.toUpperCase()}]',
        );
        showToast('❌ 진입 실패: ${response.statusCode}');
        print(
          '[ERROR] 매매 실패 [$currentMode] (${response.statusCode}): $errorMessage',
        );
      }
    } catch (e) {
      updateBotStatus('❌ 신호 처리 중 오류 발생');
      showToast('❌ 오류 발생: ${e.toString()}');
      print('[ERROR] 신호 처리 오류: $e');
    }
  }

  // 현재 포지션 조회
  static Future<String?> getCurrentPosition(
    String coin,
    String username,
    String apiBaseUrl,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/api/bot/position/$username/$coin'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['position']; // 'LONG', 'SHORT', null
      }
    } catch (e) {
      print('[ERROR] 포지션 조회 실패: $e');
    }
    return null;
  }

  // 반대 신호인지 확인
  static bool isOppositeSignal(String currentPosition, String newSignal) {
    return (currentPosition == 'LONG' && newSignal.toUpperCase() == 'SHORT') ||
        (currentPosition == 'SHORT' && newSignal.toUpperCase() == 'LONG');
  }

  // 시장가로 포지션 청산
  static Future<bool> closePositionAtMarket(
    String coin,
    String position,
    String username,
    String mode,
    String apiBaseUrl,
  ) async {
    try {
      final requestBody = {
        'username': username,
        'mode': mode, // 'demo' or 'real'
        'coin': coin,
        'position': position,
        'orderType': 'market', // 시장가 청산
        'useTestnet': false, // 🚫 테스트넷 절대 금지
        'testnetDisabled': true, // 🚫 테스트넷 비활성화
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse('$apiBaseUrl/api/bot/close-position'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        print('[SUCCESS] $position 포지션 시장가 청산 완료 [$mode]');
        return true;
      } else {
        print(
          '[ERROR] 포지션 청산 실패 [$mode] (${response.statusCode}): ${response.body}',
        );
        return false;
      }
    } catch (e) {
      print('[ERROR] 포지션 청산 오류 [$mode]: $e');
      return false;
    }
  }

  // TODO: 실제 구현 필요한 헬퍼 메소드들
  static void playNotificationSound() {
    // TODO: 실제 알림 사운드 재생 로직 구현
    print('[DEBUG] 알림 사운드 재생');
  }

  static void showToast(String message) {
    // TODO: 실제 토스트 메시지 표시 로직 구현
    print('[DEBUG] 토스트: $message');
  }

  static void updateBotStatus(String status) {
    // TODO: 실제 봇 상태 업데이트 로직 구현
    print('[DEBUG] 봇 상태: $status');
  }

  /// 🔒 API 설정 초기화 메소드 (앱 초기 실행 시 호출)
  static Future<void> initializeApiConfig() async {
    try {
      final isConfigured = await ApiSecureStorage.isCurrentModeConfigured();
      if (!isConfigured) {
        print('[WARN] ⚠️ API 설정이 완료되지 않았습니다. 설정 화면으로 이동하세요.');
      } else {
        await ApiSecureStorage.debugPrintConfig();
      }
    } catch (e) {
      print('[ERROR] API 설정 초기화 실패: $e');
    }
  }
}
