import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/trading_signal.dart';

class SignalReceiverService extends ChangeNotifier {
  Timer? _connectionTimer;
  Timer? _heartbeatTimer;
  bool _isConnected = false;
  TradingSignal? _lastSignal;

  // 서버 연결 정보
  static const Duration _reconnectInterval = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);

  // 백그라운드 알림을 위한 플랫폼 채널
  static const _platform = MethodChannel('com.opensystems.bot/background');

  bool get isConnected => _isConnected;
  TradingSignal? get lastSignal => _lastSignal;

  SignalReceiverService() {
    _initializeBackgroundService();
  }

  /// 백그라운드 서비스 초기화
  void _initializeBackgroundService() async {
    try {
      // 백그라운드 실행 권한 요청
      await _platform.invokeMethod('requestBackgroundPermission');

      // 알림 권한 요청
      await _platform.invokeMethod('requestNotificationPermission');
    } catch (e) {
      debugPrint('백그라운드 서비스 초기화 실패: $e');
    }
  }

  /// 신호 수신 시작
  void startListening() async {
    if (_isConnected) return;

    try {
      // 웹소켓 연결 시뮬레이션 (실제로는 HTTP 폴링 또는 WebSocket 사용)
      _startPollingForSignals();

      // 하트비트 시작
      _startHeartbeat();

      _isConnected = true;
      notifyListeners();

      debugPrint('신호 수신 시작됨');
    } catch (e) {
      debugPrint('신호 수신 시작 실패: $e');
      _scheduleReconnect();
    }
  }

  /// 신호 수신 중지
  void stopListening() {
    _connectionTimer?.cancel();
    _heartbeatTimer?.cancel();

    _isConnected = false;
    notifyListeners();

    debugPrint('신호 수신 중지됨');
  }

  /// 재연결
  void reconnect() {
    stopListening();
    Future.delayed(Duration(seconds: 1), () {
      startListening();
    });
  }

  /// 폴링으로 신호 확인 (실제 서버 연결 대신)
  void _startPollingForSignals() {
    _connectionTimer = Timer.periodic(Duration(seconds: 3), (timer) async {
      try {
        await _checkForNewSignals();
      } catch (e) {
        debugPrint('신호 확인 오류: $e');
        _scheduleReconnect();
      }
    });
  }

  /// 새로운 신호 확인 (시뮬레이션)
  Future<void> _checkForNewSignals() async {
    // 실제로는 서버에서 신호를 받아옴
    // 여기서는 테스트용으로 가끔 랜덤 신호 생성

    if (DateTime.now().second % 30 == 0) {
      final testSignal = TradingSignal(
        id: DateTime.now().millisecondsSinceEpoch,
        signalType: DateTime.now().second % 2 == 0 ? 'LONG' : 'SHORT',
        symbol: 'BTCUSDT',
        detectedAt: DateTime.now(),
        processed: true,
        source: 'Remote Server',
      );

      _onSignalReceived(testSignal);
    }
  }

  /// 신호 수신 처리
  void _onSignalReceived(TradingSignal signal) async {
    _lastSignal = signal;
    notifyListeners();

    // 백그라운드에서 알림 표시
    await _showSignalNotification(signal);

    // 화면 깨우기
    await _wakeUpDevice();

    debugPrint('새 신호 수신: ${signal.signalType} ${signal.symbol}');
  }

  /// 신호 알림 표시
  Future<void> _showSignalNotification(TradingSignal signal) async {
    try {
      final isLong = signal.signalType == 'LONG';
      await _platform.invokeMethod('showNotification', {
        'title': '오픈시스템즈 봇',
        'body': '${isLong ? '매수' : '매도'} 신호: ${signal.symbol}',
        'signalType': signal.signalType,
        'symbol': signal.symbol,
        'timestamp': signal.detectedAt.millisecondsSinceEpoch,
      });
    } catch (e) {
      debugPrint('알림 표시 실패: $e');
    }
  }

  /// 디바이스 깨우기
  Future<void> _wakeUpDevice() async {
    try {
      await _platform.invokeMethod('wakeUpDevice');
    } catch (e) {
      debugPrint('디바이스 깨우기 실패: $e');
    }
  }

  /// 하트비트 시작
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      _sendHeartbeat();
    });
  }

  /// 하트비트 전송
  void _sendHeartbeat() async {
    try {
      // 실제로는 서버에 하트비트 전송
      debugPrint('하트비트 전송: ${DateTime.now()}');
    } catch (e) {
      debugPrint('하트비트 전송 실패: $e');
      _scheduleReconnect();
    }
  }

  /// 재연결 스케줄링
  void _scheduleReconnect() {
    if (_isConnected) {
      _isConnected = false;
      notifyListeners();
    }

    Timer(_reconnectInterval, () {
      if (!_isConnected) {
        startListening();
      }
    });
  }

  /// 수동 테스트 신호 생성
  void generateTestSignal(String signalType) {
    final testSignal = TradingSignal(
      id: DateTime.now().millisecondsSinceEpoch,
      signalType: signalType,
      symbol: 'BTCUSDT',
      detectedAt: DateTime.now(),
      processed: true,
      source: 'Manual Test',
    );

    _onSignalReceived(testSignal);
  }

  @override
  void dispose() {
    stopListening();
    super.dispose();
  }
}
