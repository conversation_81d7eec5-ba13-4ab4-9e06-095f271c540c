// 웹용 푸시 알림 서비스 (WebSocket 통신 포함)
import 'package:flutter/foundation.dart';
import 'websocket_server_service.dart';

class PushService {
  static final PushService _instance = PushService._internal();
  factory PushService() => _instance;
  PushService._internal();

  WebSocketServerService? _webSocketService;

  Future<void> initialize() async {
    debugPrint('푸시 서비스 초기화 - WebSocket 연결 준비');
    // WebSocket 서버 서비스 참조 설정
    _webSocketService = WebSocketServerService();
  }

  // 모바일 앱으로 신호 알림 전송 (앱 깨우기 + 출력)
  Future<void> sendSignalNotification({
    required String signalType,
    String? coinName,
    String? message,
  }) async {
    try {
      final coinText = coinName != null ? ' ($coinName)' : '';
      final title = '$signalType 신호$coinText';
      final body = message ?? '새로운 매매 신호가 감지되었습니다!';

      // 콘솔 출력
      debugPrint('📱 신호 알림: $title - $body');

      // WebSocket을 통해 모바일 앱으로 전송 (앱 깨우기)
      await _sendToMobileApp({
        'type': 'signal_notification',
        'signalType': signalType,
        'coinName': coinName,
        'title': title,
        'body': body,
        'timestamp': DateTime.now().toIso8601String(),
        'priority': 'high', // 앱 깨우기용 높은 우선순위
      });
    } catch (e) {
      debugPrint('신호 알림 전송 실패: $e');
    }
  }

  // WebSocket을 통해 모바일 앱으로 데이터 전송
  Future<void> _sendToMobileApp(Map<String, dynamic> data) async {
    try {
      if (_webSocketService != null) {
        _webSocketService!.broadcastSignal(data);
        debugPrint('📡 모바일 앱으로 전송: ${data['type']}');
      }
    } catch (e) {
      debugPrint('모바일 앱 전송 오류: $e');
    }
  }

  // 일반 로컬 알림 전송
  Future<void> sendLocalNotification({
    required String title,
    required String body,
  }) async {
    await sendSignalNotification(signalType: title, message: body);
  }

  // FCM 푸시 알림 전송 (실제 구현시 사용)
  Future<void> sendPushNotification({
    required String title,
    required String body,
    String? token,
    Map<String, dynamic>? data,
  }) async {
    await sendSignalNotification(signalType: title, message: body);
  }

  // 긴급 신호 알림 (높은 우선순위로 앱 깨우기)
  Future<void> sendUrgentSignalAlert({
    required String signalType,
    required String coinName,
    required double price,
  }) async {
    await _sendToMobileApp({
      'type': 'urgent_signal',
      'signalType': signalType,
      'coinName': coinName,
      'price': price,
      'title': '🚨 긴급 $signalType 신호',
      'body': '$coinName \$${price.toStringAsFixed(6)}',
      'timestamp': DateTime.now().toIso8601String(),
      'priority': 'max', // 최고 우선순위로 앱 강제 깨우기
      'sound': 'alarm',
      'vibration': true,
    });
  }
}
