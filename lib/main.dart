import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'services/web_database_service.dart';
import 'services/auth_service.dart';
import 'services/test_signal_service.dart';
import 'screens/login_screen.dart';
import 'screens/admin_dashboard.dart';
import 'screens/user_dashboard.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize web database
  final databaseService = WebDatabaseService();
  await databaseService.initDatabase(); // 웹 데이터베이스 초기화

  // Initialize test signal service
  final testSignalService = TestSignalService();
  await testSignalService.loadState();

  runApp(
    MyApp(
      databaseService: databaseService,
      testSignalService: testSignalService,
    ),
  );
}

class MyApp extends StatelessWidget {
  final WebDatabaseService databaseService;
  final TestSignalService testSignalService;

  const MyApp({
    super.key,
    required this.databaseService,
    required this.testSignalService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<WebDatabaseService>.value(value: databaseService),
        ChangeNotifierProvider<AuthService>(create: (_) => AuthService()),
        ChangeNotifierProvider<TestSignalService>.value(
          value: testSignalService,
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(375, 812), // iPhone 13 Pro 기준 (스마트폰 규격)
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            title: '오픈시스템 관리',
            debugShowCheckedModeBanner: false,
            builder:
                (context, child) => ResponsiveBreakpoints.builder(
                  child: _buildResponsiveWrapper(child!),
                  breakpoints: [
                    const Breakpoint(start: 0, end: 450, name: MOBILE),
                    const Breakpoint(start: 451, end: 800, name: TABLET),
                    const Breakpoint(start: 801, end: 1920, name: DESKTOP),
                    const Breakpoint(
                      start: 1921,
                      end: double.infinity,
                      name: '4K',
                    ),
                  ],
                ),
            theme: _buildLightTheme(),
            darkTheme: _buildDarkTheme(),
            themeMode: ThemeMode.dark, // 다크 테마 강제 설정
            home: const AuthWrapper(),
          );
        },
      ),
    );
  }

  // 반응형 래퍼 - 웹에서 센터에 스마트폰 크기로 표시
  Widget _buildResponsiveWrapper(Widget child) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 웹 환경에서 큰 화면일 때 센터에 스마트폰 크기로 제한
        if (constraints.maxWidth > 500) {
          return Container(
            color: Colors.black87, // 배경색
            child: Center(
              child: Container(
                width: 375, // 스마트폰 너비 고정
                height: constraints.maxHeight,
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: child,
              ),
            ),
          );
        }
        return child;
      },
    );
  }

  // 라이트 테마
  ThemeData _buildLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  // 다크 테마 (Material 3)
  ThemeData _buildDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      textTheme: TextTheme(
        bodySmall: TextStyle(fontSize: 12.sp),
        bodyMedium: TextStyle(fontSize: 14.sp),
        bodyLarge: TextStyle(fontSize: 16.sp),
        titleSmall: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
        titleLarge: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
      ),
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 0,
        titleTextStyle: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: Size(double.infinity, 48.h),
          textStyle: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.blue, width: 2),
        ),
      ),
    );
  }
}

// 인증 래퍼
class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        if (authService.isLoggedIn) {
          if (authService.isAdmin) {
            return const AdminDashboard();
          } else {
            return const UserDashboard();
          }
        } else {
          return const LoginScreen();
        }
      },
    );
  }
}
