class TradingSignal {
  final int id;
  final String signalType; // 'LONG', 'SHORT'
  final String symbol; // 'BTCUSDT', 'ETHUSDT' 등
  final DateTime detectedAt;
  bool processed;
  final String source; // 'LogFile', 'Manual', 'API' 등
  final String? rawLogLine;
  final String? targetApp; // 신호를 전송할 대상 앱
  final String? response; // 전송 응답
  final DateTime? sentAt; // 전송 시간

  TradingSignal({
    required this.id,
    required this.signalType,
    required this.symbol,
    required this.detectedAt,
    required this.processed,
    required this.source,
    this.rawLogLine,
    this.targetApp,
    this.response,
    this.sentAt,
  });

  // timestamp getter 추가 (detectedAt과 동일)
  DateTime get timestamp => detectedAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'signal_type': signalType,
      'symbol': symbol,
      'detected_at': detectedAt.toIso8601String(),
      'processed': processed ? 1 : 0,
      'source': source,
      'raw_log_line': rawLogLine,
      'target_app': targetApp,
      'response': response,
      'sent_at': sentAt?.toIso8601String(),
    };
  }

  factory TradingSignal.fromMap(Map<String, dynamic> map) {
    return TradingSignal(
      id: map['id'] ?? 0,
      signalType: map['signal_type'] ?? '',
      symbol: map['symbol'] ?? '',
      detectedAt: DateTime.parse(map['detected_at']),
      processed: (map['processed'] ?? 0) == 1,
      source: map['source'] ?? '',
      rawLogLine: map['raw_log_line'],
      targetApp: map['target_app'],
      response: map['response'],
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
    );
  }

  // JSON 직렬화 메소드들
  Map<String, dynamic> toJson() => toMap();

  factory TradingSignal.fromJson(Map<String, dynamic> json) {
    return TradingSignal(
      id: json['id'] as int,
      signalType: json['signal_type'] as String,
      symbol: json['symbol'] as String,
      detectedAt: DateTime.parse(json['detected_at'] as String),
      processed: (json['processed'] as int) == 1,
      source: json['source'] as String,
      rawLogLine: json['raw_log_line'] as String?,
      targetApp: json['target_app'] as String?,
      response: json['response'] as String?,
      sentAt: json['sent_at'] != null
          ? DateTime.parse(json['sent_at'] as String)
          : null,
    );
  }

  TradingSignal copyWith({
    int? id,
    String? signalType,
    String? symbol,
    DateTime? detectedAt,
    bool? processed,
    String? source,
    String? rawLogLine,
    String? targetApp,
    String? response,
    DateTime? sentAt,
  }) {
    return TradingSignal(
      id: id ?? this.id,
      signalType: signalType ?? this.signalType,
      symbol: symbol ?? this.symbol,
      detectedAt: detectedAt ?? this.detectedAt,
      processed: processed ?? this.processed,
      source: source ?? this.source,
      rawLogLine: rawLogLine ?? this.rawLogLine,
      targetApp: targetApp ?? this.targetApp,
      response: response ?? this.response,
      sentAt: sentAt ?? this.sentAt,
    );
  }

  @override
  String toString() {
    return 'TradingSignal(id: $id, type: $signalType, symbol: $symbol, time: $detectedAt)';
  }
}
