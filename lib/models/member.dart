class Member {
  final int id;
  final String username;
  String password; // 해시된 비밀번호
  String email;
  final String? phone;
  final String status; // 'admin', 'user', 'pending', 'inactive'
  final String role; // 'admin', 'user'
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final String? pushToken;

  Member({
    required this.id,
    required this.username,
    required this.password,
    required this.email,
    this.phone,
    required this.status,
    required this.role,
    required this.isActive,
    required this.createdAt,
    this.lastLogin,
    this.pushToken,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'email': email,
      'phone': phone,
      'status': status,
      'role': role,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'push_token': pushToken,
    };
  }

  factory Member.fromMap(Map<String, dynamic> map) {
    return Member(
      id: map['id'] ?? 0,
      username: map['username'] ?? '',
      password: map['password'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'],
      status: map['status'] ?? 'user',
      role: map['role'] ?? 'user',
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      lastLogin: map['last_login'] != null ? DateTime.parse(map['last_login']) : null,
      pushToken: map['push_token'],
    );
  }

  // JSON 직렬화 메소드들
  Map<String, dynamic> toJson() => toMap();

  factory Member.fromJson(Map<String, dynamic> json) {
    return Member(
      id: json['id'] as int,
      username: json['username'] as String,
      password: json['password'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      status: json['status'] as String,
      role: json['role'] as String,
      isActive: (json['is_active'] as int) == 1,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLogin: json['last_login'] != null 
          ? DateTime.parse(json['last_login'] as String) 
          : null,
      pushToken: json['push_token'] as String?,
    );
  }

  Member copyWith({
    int? id,
    String? username,
    String? password,
    String? email,
    String? phone,
    String? status,
    String? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
    String? pushToken,
  }) {
    return Member(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      status: status ?? this.status,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      pushToken: pushToken ?? this.pushToken,
    );
  }
}
