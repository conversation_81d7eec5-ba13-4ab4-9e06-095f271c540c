class AdminSettings {
  final int id;
  final String settingKey;
  final String settingValue;
  final String settingType; // string, number, boolean, json
  final String? description;
  final DateTime lastUpdated;
  final String updatedBy;

  AdminSettings({
    required this.id,
    required this.settingKey,
    required this.settingValue,
    required this.settingType,
    this.description,
    required this.lastUpdated,
    required this.updatedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'setting_key': settingKey,
      'setting_value': settingValue,
      'setting_type': settingType,
      'description': description,
      'last_updated': lastUpdated.toIso8601String(),
      'updated_by': updatedBy,
    };
  }

  factory AdminSettings.fromMap(Map<String, dynamic> map) {
    return AdminSettings(
      id: map['id'] ?? 0,
      settingKey: map['setting_key'] ?? '',
      settingValue: map['setting_value'] ?? '',
      settingType: map['setting_type'] ?? 'string',
      description: map['description'],
      lastUpdated: DateTime.parse(map['last_updated']),
      updatedBy: map['updated_by'] ?? '',
    );
  }

  AdminSettings copyWith({
    int? id,
    String? settingKey,
    String? settingValue,
    String? settingType,
    String? description,
    DateTime? lastUpdated,
    String? updatedBy,
  }) {
    return AdminSettings(
      id: id ?? this.id,
      settingKey: settingKey ?? this.settingKey,
      settingValue: settingValue ?? this.settingValue,
      settingType: settingType ?? this.settingType,
      description: description ?? this.description,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}
