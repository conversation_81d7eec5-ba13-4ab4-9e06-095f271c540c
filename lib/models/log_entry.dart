class LogEntry {
  final int id;
  final String level;
  final String category; // source를 category로 변경
  final String message;
  final DateTime timestamp;
  final String? details;

  LogEntry({
    required this.id,
    required this.level,
    required this.category,
    required this.message,
    required this.timestamp,
    this.details,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'level': level,
      'category': category,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'details': details,
    };
  }

  factory LogEntry.fromMap(Map<String, dynamic> map) {
    return LogEntry(
      id: map['id'] ?? 0,
      level: map['level'] ?? '',
      category: map['category'] ?? '',
      message: map['message'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      details: map['details'],
    );
  }

  // JSON 직렬화 메소드들
  Map<String, dynamic> toJson() => toMap();

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      id: json['id'] as int,
      level: json['level'] as String,
      category: json['category'] as String,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      details: json['details'] as String?,
    );
  }
}
