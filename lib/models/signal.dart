class Signal {
  final int id;
  final String signalType;
  final String targetApp;
  final String payload;
  String status;
  final DateTime createdAt;
  DateTime? sentAt;
  String? response;

  Signal({
    required this.id,
    required this.signalType,
    required this.targetApp,
    required this.payload,
    required this.status,
    required this.createdAt,
    this.sentAt,
    this.response,
  });

  // timestamp getter 추가 (createdAt과 동일)
  DateTime get timestamp => createdAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'signal_type': signalType,
      'target_app': targetApp,
      'payload': payload,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'response': response,
    };
  }

  factory Signal.fromMap(Map<String, dynamic> map) {
    return Signal(
      id: map['id'] ?? 0,
      signalType: map['signal_type'] ?? '',
      targetApp: map['target_app'] ?? '',
      payload: map['payload'] ?? '',
      status: map['status'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      sentAt: map['sent_at'] != null ? DateTime.parse(map['sent_at']) : null,
      response: map['response'],
    );
  }

  // JSON 직렬화 메소드들
  Map<String, dynamic> toJson() => toMap();

  factory Signal.fromJson(Map<String, dynamic> json) {
    return Signal(
      id: json['id'] as int,
      signalType: json['signal_type'] as String,
      targetApp: json['target_app'] as String,
      payload: json['payload'] as String,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      sentAt: json['sent_at'] != null
          ? DateTime.parse(json['sent_at'] as String)
          : null,
      response: json['response'] as String?,
    );
  }
}
