class SystemStatus {
  final int id;
  final String serverStatus;
  final DateTime lastUpdated;
  final double? cpuUsage;
  final double? memoryUsage;
  final double? diskUsage;
  final int? activeConnections;

  SystemStatus({
    required this.id,
    required this.serverStatus,
    required this.lastUpdated,
    this.cpuUsage,
    this.memoryUsage,
    this.diskUsage,
    this.activeConnections,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'server_status': serverStatus,
      'last_updated': lastUpdated.toIso8601String(),
      'cpu_usage': cpuUsage,
      'memory_usage': memoryUsage,
      'disk_usage': diskUsage,
      'active_connections': activeConnections,
    };
  }

  factory SystemStatus.fromMap(Map<String, dynamic> map) {
    return SystemStatus(
      id: map['id'] ?? 0,
      serverStatus: map['server_status'] ?? '',
      lastUpdated: DateTime.parse(map['last_updated']),
      cpuUsage: map['cpu_usage']?.toDouble(),
      memoryUsage: map['memory_usage']?.toDouble(),
      diskUsage: map['disk_usage']?.toDouble(),
      activeConnections: map['active_connections'],
    );
  }

  // JSON 직렬화 메소드들
  Map<String, dynamic> toJson() => toMap();

  factory SystemStatus.fromJson(Map<String, dynamic> json) {
    return SystemStatus(
      id: json['id'] ?? 0,
      serverStatus: json['server_status'] ?? '',
      lastUpdated: DateTime.parse(json['last_updated']),
      cpuUsage: json['cpu_usage']?.toDouble(),
      memoryUsage: json['memory_usage']?.toDouble(),
      diskUsage: json['disk_usage']?.toDouble(),
      activeConnections: json['active_connections'],
    );
  }
}
