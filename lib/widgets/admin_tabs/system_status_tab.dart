import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SystemStatusTab extends StatefulWidget {
  const SystemStatusTab({Key? key}) : super(key: key);

  @override
  _SystemStatusTabState createState() => _SystemStatusTabState();
}

class _SystemStatusTabState extends State<SystemStatusTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildServerStatus(),
            SizedBox(height: 20.h),
            _buildSystemInfo(),
            SizedBox(height: 20.h),
            _buildConnectionStatus(),
          ],
        ),
      ),
    );
  }

  Widget _buildServerStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '서버 상태',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            <PERSON><PERSON>Box(height: 16.h),
            Row(
              children: [
                Icon(Icons.circle, color: Colors.green, size: 12.sp),
                SizedBox(width: 8.w),
                Text('WebSocket 서버 실행 중', style: TextStyle(fontSize: 14.sp)),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              '포트: 8080',
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemInfo() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '시스템 정보',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildInfoRow('버전', '1.0.0'),
            _buildInfoRow('빌드', '20250626'),
            _buildInfoRow('플랫폼', 'Flutter'),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '연결 상태',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildInfoRow('연결된 클라이언트', '0'),
            _buildInfoRow('총 신호 전송', '0'),
            _buildInfoRow('마지막 신호', '없음'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 14.sp),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
