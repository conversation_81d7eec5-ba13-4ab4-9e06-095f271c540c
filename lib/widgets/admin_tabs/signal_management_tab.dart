import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../services/signal_detection_service.dart';
import '../../services/push_service.dart';
import '../../services/test_signal_service.dart';

class SignalManagementTab extends StatefulWidget {
  const SignalManagementTab({Key? key}) : super(key: key);

  @override
  _SignalManagementTabState createState() => _SignalManagementTabState();
}

class _SignalManagementTabState extends State<SignalManagementTab> {
  late SignalDetectionService _signalService;
  TestSignalService? _testSignalService;
  
  // 바이비트 선물 무기한 지원 코인들
  final List<Map<String, String>> _bybitFuturesCoins = [
    {'symbol': 'BTCUSDT', 'name': '비트코인 (BTC)'},
    {'symbol': 'ETHUSDT', 'name': '이더리움 (ETH)'},
    {'symbol': 'XRPUSDT', 'name': '리플 (XRP)'},
    {'symbol': 'SOLUSDT', 'name': '솔라나 (SOL)'},
    {'symbol': 'WLDUSDT', 'name': '월드코인 (WLD)'},
    {'symbol': 'DOGEUSDT', 'name': '도지코인 (DOGE)'},
    {'symbol': 'ADAUSDT', 'name': '에이다 (ADA)'},
    {'symbol': 'DOTUSDT', 'name': '폴카닷 (DOT)'},
    {'symbol': 'LINKUSDT', 'name': '체인링크 (LINK)'},
    {'symbol': 'LTCUSDT', 'name': '라이트코인 (LTC)'},
  ];
  
  String _selectedCoin = 'BTCUSDT';
  String _selectedSignalType = 'LONG';
  bool _isMonitoring = false;
  String? _currentLogFile;
  Map<String, dynamic> _signalStats = {};

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadSignalStats();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Provider에서 TestSignalService 가져오기
    _testSignalService = Provider.of<TestSignalService>(context, listen: false);
    _testSignalService?.addListener(_onTestSignalServiceChanged);
  }

  void _initializeServices() {
    _signalService = SignalDetectionService(
      PushService(),
    );
    
    _signalService.addListener(_onSignalServiceChanged);
  }

  void _onTestSignalServiceChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  void _onSignalServiceChanged() {
    if (mounted) {
      setState(() {
        _isMonitoring = _signalService.isMonitoring;
        _currentLogFile = _signalService.currentLogFile;
      });
      _loadSignalStats();
    }
  }

  Future<void> _loadSignalStats() async {
    try {
      final stats = await _signalService.getSignalStats();
      setState(() {
        _signalStats = stats;
      });
    } catch (e) {
      print('통계 로드 실패: $e');
    }
  }

  Future<void> _startMonitoring() async {
    try {
      final logFile = '/Users/<USER>/Documents/opensystems_admin/capture_log.txt';
      await _signalService.startMonitoring(logFile);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('로그 파일 모니터링을 시작했습니다.'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('모니터링 시작 실패: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _stopMonitoring() async {
    try {
      await _signalService.stopMonitoring();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('로그 파일 모니터링을 중지했습니다.'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('모니터링 중지 실패: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendManualSignal() async {
    try {
      await _signalService.sendManualSignal(_selectedSignalType, _selectedCoin);
      
      final coinName = _bybitFuturesCoins.firstWhere(
        (coin) => coin['symbol'] == _selectedCoin,
        orElse: () => {'name': _selectedCoin},
      )['name'];
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('수동 신호 전송 완료: $_selectedSignalType - $coinName'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('신호 전송 실패: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _signalService.removeListener(_onSignalServiceChanged);
    _testSignalService?.removeListener(_onTestSignalServiceChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTestSignalControl(),
            SizedBox(height: 20.h),
            _buildMonitoringControl(),
            SizedBox(height: 20.h),
            _buildManualSignalSender(),
            SizedBox(height: 20.h),
            _buildSignalStats(),
            SizedBox(height: 20.h),
            _buildRecentSignals(),
          ],
        ),
      ),
    );
  }

  Widget _buildMonitoringControl() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monitor, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  '로그 파일 모니터링',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _isMonitoring ? Colors.green : Colors.red,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  _isMonitoring ? '모니터링 실행 중' : '모니터링 중지됨',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ],
            ),
            if (_currentLogFile != null) ...[
              SizedBox(height: 8.h),
              Text(
                '파일: ${_currentLogFile!.split('/').last}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
            SizedBox(height: 16.h),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isMonitoring ? null : _startMonitoring,
                  icon: Icon(Icons.play_arrow, size: 16.sp),
                  label: Text('시작', style: TextStyle(fontSize: 14.sp)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(width: 12.w),
                ElevatedButton.icon(
                  onPressed: _isMonitoring ? _stopMonitoring : null,
                  icon: Icon(Icons.stop, size: 16.sp),
                  label: Text('중지', style: TextStyle(fontSize: 14.sp)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualSignalSender() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.send, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  '수동 신호 전송',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // 신호 타입 선택
            Text(
              '신호 타입',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: Text('롱 (LONG)', style: TextStyle(fontSize: 14.sp)),
                    value: 'LONG',
                    groupValue: _selectedSignalType,
                    onChanged: (value) {
                      setState(() {
                        _selectedSignalType = value!;
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: Text('숏 (SHORT)', style: TextStyle(fontSize: 14.sp)),
                    value: 'SHORT',
                    groupValue: _selectedSignalType,
                    onChanged: (value) {
                      setState(() {
                        _selectedSignalType = value!;
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // 바이비트 선물 코인 선택
            Text(
              '바이비트 선물 무기한 코인 선택',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedCoin,
                  isExpanded: true,
                  items: _bybitFuturesCoins.map((coin) {
                    return DropdownMenuItem<String>(
                      value: coin['symbol'],
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              coin['symbol']!,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue[700],
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              coin['name']!,
                              style: TextStyle(fontSize: 14.sp),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCoin = value!;
                    });
                  },
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // 전송 버튼
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _sendManualSignal,
                icon: Icon(Icons.send, size: 16.sp),
                label: Text(
                  '선택된 신호 전송 ($_selectedSignalType - $_selectedCoin)',
                  style: TextStyle(fontSize: 14.sp),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _selectedSignalType == 'LONG' 
                      ? Colors.green 
                      : Colors.red,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignalStats() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  '신호 통계',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    '총 신호',
                    '${_signalStats['total_signals'] ?? 0}',
                    Colors.blue,
                    Icons.signal_cellular_alt,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildStatCard(
                    '롱 신호',
                    '${_signalStats['long_signals'] ?? 0}',
                    Colors.green,
                    Icons.trending_up,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildStatCard(
                    '숏 신호',
                    '${_signalStats['short_signals'] ?? 0}',
                    Colors.red,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            if (_signalStats['last_signal'] != null) ...[
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '마지막 신호',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${_signalStats['last_signal']} - ${_signalStats['last_coin'] ?? 'N/A'}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentSignals() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  '최근 신호 목록',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Container(
              height: 200.h,
              child: ListView.builder(
                itemCount: 5, // 임시 데이터
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: index % 2 == 0 ? Colors.green : Colors.red,
                      child: Icon(
                        index % 2 == 0 ? Icons.trending_up : Icons.trending_down,
                        color: Colors.white,
                        size: 16.sp,
                      ),
                    ),
                    title: Text(
                      '${index % 2 == 0 ? 'LONG' : 'SHORT'} - BTCUSDT',
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    subtitle: Text(
                      '${DateTime.now().subtract(Duration(minutes: index * 15)).toString().substring(0, 19)}',
                      style: TextStyle(fontSize: 12.sp),
                    ),
                    trailing: Chip(
                      label: Text(
                        '전송완료',
                        style: TextStyle(fontSize: 10.sp),
                      ),
                      backgroundColor: Colors.blue[50],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSignalControl() {
    if (_testSignalService == null) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: Text(
              'TestSignalService 로딩 중...',
              style: TextStyle(fontSize: 14.sp),
            ),
          ),
        ),
      );
    }

    final service = _testSignalService!;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.science, size: 20.sp, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 8.w),
                Text(
                  '웹 내부 테스트 신호 생성기',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            
            // 상태 표시
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: service.isRunning 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: service.isRunning 
                    ? Colors.green
                    : Colors.red,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 12.w,
                    height: 12.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: service.isRunning ? Colors.green : Colors.red,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    service.isRunning ? '테스트 신호 생성 중 (5초마다)' : '테스트 신호 중지됨',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: service.isRunning ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                  Spacer(),
                  Text(
                    '생성된 신호: ${service.signalCount}개',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // 제어 버튼들
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: service.isRunning ? null : () async {
                      await service.startTestSignals();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('테스트 신호 생성기가 시작되었습니다.'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    },
                    icon: Icon(Icons.play_arrow),
                    label: Text('시작'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: !service.isRunning ? null : () async {
                      await service.stopTestSignals();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('테스트 신호 생성기가 중지되었습니다.'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                    icon: Icon(Icons.stop),
                    label: Text('중지'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      await service.generateSingleSignal();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('단일 테스트 신호가 생성되었습니다.'),
                          backgroundColor: Colors.blue,
                        ),
                      );
                    },
                    icon: Icon(Icons.add),
                    label: Text('단일 생성'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // 최근 생성된 신호들
            if (service.recentSignals.isNotEmpty) ...[
              Text(
                '최근 생성된 테스트 신호들',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: 8.h),
              Container(
                height: 120.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: ListView.builder(
                  padding: EdgeInsets.all(8.w),
                  itemCount: service.recentSignals.length,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
                      margin: EdgeInsets.only(bottom: 2.h),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        service.recentSignals[index],
                        style: TextStyle(
                          fontSize: 11.sp,
                          fontFamily: 'monospace',
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            
            SizedBox(height: 12.h),
            
            // 로그 관리 버튼들
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final logContent = await service.getLogContent();
                      if (logContent.isNotEmpty) {
                        _showLogDialog(logContent);
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('로그가 비어있습니다.')),
                        );
                      }
                    },
                    icon: Icon(Icons.visibility),
                    label: Text('로그 보기'),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final confirmed = await _showClearConfirmDialog();
                      if (confirmed == true) {
                        await service.clearLogs();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('테스트 로그가 삭제되었습니다.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    icon: Icon(Icons.clear_all),
                    label: Text('로그 삭제'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showLogDialog(String logContent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('테스트 신호 로그'),
        content: Container(
          width: double.maxFinite,
          height: 400.h,
          child: SingleChildScrollView(
            child: Text(
              logContent,
              style: TextStyle(
                fontSize: 11.sp,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('닫기'),
          ),
        ],
      ),
    );
  }

  Future<bool?> _showClearConfirmDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('로그 삭제 확인'),
        content: Text('모든 테스트 신호 로그를 삭제하시겠습니까?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('취소'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: Text('삭제'),
          ),
        ],
      ),
    );
  }
}
