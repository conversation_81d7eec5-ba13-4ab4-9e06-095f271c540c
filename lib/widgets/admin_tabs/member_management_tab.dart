import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MemberManagementTab extends StatefulWidget {
  const MemberManagementTab({Key? key}) : super(key: key);

  @override
  _MemberManagementTabState createState() => _MemberManagementTabState();
}

class _MemberManagementTabState extends State<MemberManagementTab> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '회원 관리',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Expanded(
            child: Card(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Center(
                  child: Text(
                    '회원 관리 기능 준비 중...',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
