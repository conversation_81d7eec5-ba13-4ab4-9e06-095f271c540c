import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class LogFileUploader extends StatefulWidget {
  final Function(String) onFileLoaded;

  const LogFileUploader({super.key, required this.onFileLoaded});

  @override
  State<LogFileUploader> createState() => _LogFileUploaderState();
}

class _LogFileUploaderState extends State<LogFileUploader> {
  String? _fileName;
  bool _isLoading = false;
  void _handleFileSelect() async {
    if (!kIsWeb) {
      // 웹이 아닌 환경에서는 파일 업로드 지원하지 않음
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('파일 업로드는 웹 환경에서만 지원됩니다.')),
        );
      }
      return;
    }

    // 웹 환경에서의 파일 업로드는 현재 비활성화
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('파일 업로드 기능은 현재 비활성화되어 있습니다.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '실제 로그 파일 로드',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (_fileName != null)
              Text(
                '로드된 파일: $_fileName',
                style: TextStyle(color: Colors.green[600]),
              ),
            const SizedBox(height: 12),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _handleFileSelect,
                  icon:
                      _isLoading
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.upload_file),
                  label: Text(_isLoading ? '로딩중...' : '로그 파일 선택'),
                ),
                const SizedBox(width: 12),
                const Text('capture_log.txt 파일을 선택하세요'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
