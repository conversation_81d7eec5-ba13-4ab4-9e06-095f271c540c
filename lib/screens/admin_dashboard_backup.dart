import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';
import '../../services/auth_service.dart';
import 'login_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _logout() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    authService.logout();

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveBreakpoints.of(context).isMobile;
    final authService = Provider.of<AuthService>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '오픈시스템 관리자',
          style: TextStyle(fontSize: isMobile ? 14.sp : 16.sp),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        toolbarHeight: isMobile ? 40.h : 50.h,
        actions: [
          // 사용자 정보
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 16.w),
                SizedBox(width: 4.w),
                Text(
                  authService.currentUser?.username ?? '',
                  style: TextStyle(fontSize: 11.sp),
                ),
              ],
            ),
          ),

          // 로그아웃 버튼
          IconButton(
            onPressed: _logout,
            icon: Icon(Icons.logout, size: 18.w),
            tooltip: '로그아웃',
          ),
        ],
        bottom:
            isMobile
                ? null
                : TabBar(
                  controller: _tabController,
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.white70,
                  indicatorColor: Colors.white,
                  labelStyle: TextStyle(fontSize: 11.sp),
                  tabs: [
                    Tab(icon: Icon(Icons.dashboard, size: 16.w), text: '시스템'),
                    Tab(icon: Icon(Icons.people, size: 16.w), text: '회원'),
                    Tab(icon: Icon(Icons.send, size: 16.w), text: '신호'),
                    Tab(icon: Icon(Icons.list_alt, size: 16.w), text: '로그'),
                    Tab(icon: Icon(Icons.settings, size: 16.w), text: '설정'),
                  ],
                ),
      ),
      body:
          isMobile
              ? _buildMobileView()
              : TabBarView(
                controller: _tabController,
                children: [
                  const SystemStatusTab(),
                  const MemberManagementTab(),
                  const SignalManagementTab(),
                  const LogManagementTab(),
                  const AdminSettingsTab(),
                ],
              ),
      bottomNavigationBar:
          isMobile
              ? BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                currentIndex: _currentIndex,
                onTap: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                selectedItemColor: Theme.of(context).primaryColor,
                unselectedItemColor: Colors.grey,
                selectedLabelStyle: TextStyle(fontSize: 10.sp),
                unselectedLabelStyle: TextStyle(fontSize: 10.sp),
                items: [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.dashboard, size: 20.w),
                    label: '시스템',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.people, size: 20.w),
                    label: '회원',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.send, size: 20.w),
                    label: '신호',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.list_alt, size: 20.w),
                    label: '로그',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.settings, size: 20.w),
                    label: '설정',
                  ),
                ],
              )
              : null,
    );
  }

  Widget _buildMobileView() {
    switch (_currentIndex) {
      case 0:
        return const SystemStatusTab();
      case 1:
        return const MemberManagementTab();
      case 2:
        return const SignalManagementTab();
      case 3:
        return const LogManagementTab();
      case 4:
        return const AdminSettingsTab();
      default:
        return const SystemStatusTab();
    }
  }
}

// 시스템 상태 탭
class SystemStatusTab extends StatelessWidget {
  const SystemStatusTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          // 서버 상태 카드
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.computer, size: 20.w, color: Colors.green),
                      SizedBox(width: 8.w),
                      Text(
                        '서버 상태',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('상태:', style: TextStyle(fontSize: 12.sp)),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          '실행중',
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('포트:', style: TextStyle(fontSize: 12.sp)),
                      Text('8080', style: TextStyle(fontSize: 12.sp)),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 8.h),

          // 시스템 리소스 카드
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '시스템 리소스',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  _buildResourceItem('CPU 사용률', '15%', Colors.blue),
                  _buildResourceItem('메모리 사용률', '32%', Colors.green),
                  _buildResourceItem('디스크 사용률', '68%', Colors.orange),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceItem(String label, String value, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontSize: 11.sp)),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 11.sp,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 회원 관리 탭
class MemberManagementTab extends StatelessWidget {
  const MemberManagementTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          // 통계 카드
          Row(
            children: [
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Column(
                      children: [
                        Text('총 회원', style: TextStyle(fontSize: 11.sp)),
                        Text(
                          '12',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Column(
                      children: [
                        Text('승인 대기', style: TextStyle(fontSize: 11.sp)),
                        Text(
                          '3',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          // 회원 목록
          Expanded(
            child: Card(
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '회원 목록',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: () {},
                          icon: Icon(Icons.refresh, size: 16.w),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: 5,
                      itemBuilder: (context, index) {
                        return ListTile(
                          dense: true,
                          leading: CircleAvatar(
                            radius: 16.w,
                            child: Text(
                              'U${index + 1}',
                              style: TextStyle(fontSize: 10.sp),
                            ),
                          ),
                          title: Text(
                            '사용자${index + 1}',
                            style: TextStyle(fontSize: 11.sp),
                          ),
                          subtitle: Text(
                            'user${index + 1}@example.com',
                            style: TextStyle(fontSize: 10.sp),
                          ),
                          trailing: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: index == 0 ? Colors.orange : Colors.green,
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              index == 0 ? '대기' : '활성',
                              style: TextStyle(
                                fontSize: 9.sp,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 신호 관리 탭
class SignalManagementTab extends StatelessWidget {
  const SignalManagementTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          // 신호 전송 버튼
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '신호 전송',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            minimumSize: Size(0, 32.h),
                          ),
                          child: Text(
                            '매수 신호',
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: Size(0, 32.h),
                          ),
                          child: Text(
                            '매도 신호',
                            style: TextStyle(fontSize: 11.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 8.h),

          // 신호 히스토리
          Expanded(
            child: Card(
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Text(
                      '신호 히스토리',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: 10,
                      itemBuilder: (context, index) {
                        final isBuy = index % 2 == 0;
                        return ListTile(
                          dense: true,
                          leading: Container(
                            width: 24.w,
                            height: 24.w,
                            decoration: BoxDecoration(
                              color: isBuy ? Colors.green : Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              isBuy ? Icons.arrow_upward : Icons.arrow_downward,
                              color: Colors.white,
                              size: 14.w,
                            ),
                          ),
                          title: Text(
                            '${isBuy ? "매수" : "매도"} 신호',
                            style: TextStyle(fontSize: 11.sp),
                          ),
                          subtitle: Text(
                            '${DateTime.now().toString().substring(0, 16)}',
                            style: TextStyle(fontSize: 10.sp),
                          ),
                          trailing: Text(
                            '성공',
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.green,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 로그 관리 탭
class LogManagementTab extends StatelessWidget {
  const LogManagementTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          // 로그 필터
          Card(
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: '레벨',
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                      ),
                      items:
                          ['ALL', 'INFO', 'WARNING', 'ERROR']
                              .map(
                                (level) => DropdownMenuItem(
                                  value: level,
                                  child: Text(
                                    level,
                                    style: TextStyle(fontSize: 11.sp),
                                  ),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {},
                    ),
                  ),
                  SizedBox(width: 8.w),
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.clear_all, size: 16.w),
                    tooltip: '로그 지우기',
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 8.h),

          // 로그 목록
          Expanded(
            child: Card(
              child: ListView.builder(
                itemCount: 20,
                itemBuilder: (context, index) {
                  final levels = ['INFO', 'WARNING', 'ERROR'];
                  final level = levels[index % 3];
                  final colors = [Colors.blue, Colors.orange, Colors.red];
                  final color = colors[index % 3];

                  return ListTile(
                    dense: true,
                    leading: Container(
                      width: 20.w,
                      height: 20.w,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          level[0],
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    title: Text(
                      '시스템 메시지 $index',
                      style: TextStyle(fontSize: 11.sp),
                    ),
                    subtitle: Text(
                      '${DateTime.now().toString().substring(0, 19)}',
                      style: TextStyle(fontSize: 9.sp),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 관리자 설정 탭
class AdminSettingsTab extends StatelessWidget {
  const AdminSettingsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          // 서버 설정
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '서버 설정',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: '8080',
                          decoration: InputDecoration(
                            labelText: '포트',
                            isDense: true,
                          ),
                          style: TextStyle(fontSize: 11.sp),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(60.w, 32.h),
                        ),
                        child: Text('적용', style: TextStyle(fontSize: 11.sp)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 8.h),

          // 로그 파일 설정
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '로그 파일 감시',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          decoration: InputDecoration(
                            labelText: '로그 파일 경로',
                            isDense: true,
                          ),
                          style: TextStyle(fontSize: 11.sp),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      IconButton(
                        onPressed: () {},
                        icon: Icon(Icons.folder_open, size: 16.w),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          minimumSize: Size(60.w, 32.h),
                        ),
                        child: Text('시작', style: TextStyle(fontSize: 11.sp)),
                      ),
                      SizedBox(width: 8.w),
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          minimumSize: Size(60.w, 32.h),
                        ),
                        child: Text('중지', style: TextStyle(fontSize: 11.sp)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 8.h),

          // 시스템 정보
          Card(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '시스템 정보',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  _buildInfoItem('버전', '1.0.0'),
                  _buildInfoItem('빌드', '2025.06.26'),
                  _buildInfoItem('데이터베이스', 'LocalStorage'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontSize: 11.sp)),
          Text(
            value,
            style: TextStyle(fontSize: 11.sp, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
