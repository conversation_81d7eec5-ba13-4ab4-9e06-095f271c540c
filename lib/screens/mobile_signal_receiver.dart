import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/signal_receiver_service.dart';
import '../models/trading_signal.dart';

class MobileSignalReceiver extends StatefulWidget {
  const MobileSignalReceiver({super.key});

  @override
  State<MobileSignalReceiver> createState() => _MobileSignalReceiverState();
}

class _MobileSignalReceiverState extends State<MobileSignalReceiver>
    with WidgetsBindingObserver {
  bool _isConnected = false;
  TradingSignal? _lastSignal;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeSignalReceiver();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 앱이 포그라운드로 돌아왔을 때
        _refreshConnection();
        break;
      case AppLifecycleState.paused:
        // 앱이 백그라운드로 갔을 때 - 백그라운드 서비스 유지
        break;
      case AppLifecycleState.detached:
        // 앱이 종료될 때
        break;
      case AppLifecycleState.inactive:
        // 앱이 비활성화될 때
        break;
      case AppLifecycleState.hidden:
        // 앱이 숨겨질 때
        break;
    }
  }

  void _initializeSignalReceiver() {
    final signalService = Provider.of<SignalReceiverService>(
      context,
      listen: false,
    );
    signalService.startListening();

    // 연결 상태 리스너
    signalService.addListener(() {
      if (mounted) {
        setState(() {
          _isConnected = signalService.isConnected;
          _lastSignal = signalService.lastSignal;
        });
      }
    });
  }

  void _refreshConnection() {
    final signalService = Provider.of<SignalReceiverService>(
      context,
      listen: false,
    );
    signalService.reconnect();
  }

  void _toggleConnection() {
    final signalService = Provider.of<SignalReceiverService>(
      context,
      listen: false,
    );
    if (_isConnected) {
      signalService.stopListening();
    } else {
      signalService.startListening();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          '오픈시스템즈 봇',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
        actions: [
          // 연결 상태 표시
          Container(
            margin: EdgeInsets.only(right: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: _isConnected ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              _isConnected ? 'ON' : 'OFF',
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black, Colors.grey[900]!, Colors.black],
          ),
        ),
        child: Column(
          children: [
            // 상태 정보 섹션
            _buildStatusSection(),

            // 신호 표시 섹션
            Expanded(child: _buildSignalSection()),

            // 하단 컨트롤
            _buildControlSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: _isConnected ? Colors.green : Colors.red,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '연결 상태',
                style: TextStyle(fontSize: 14.sp, color: Colors.white70),
              ),
              Row(
                children: [
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: _isConnected ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    _isConnected ? '연결됨' : '연결 끊어짐',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _isConnected ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '마지막 업데이트',
                style: TextStyle(fontSize: 12.sp, color: Colors.white70),
              ),
              Text(
                DateTime.now().toString().substring(0, 19),
                style: TextStyle(fontSize: 12.sp, color: Colors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSignalSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child:
          _lastSignal == null
              ? _buildNoSignalWidget()
              : _buildSignalWidget(_lastSignal!),
    );
  }

  Widget _buildNoSignalWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.signal_cellular_off,
              size: 40.w,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 20.h),
          Text(
            '신호 대기 중',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '새로운 거래 신호를 기다리고 있습니다',
            style: TextStyle(fontSize: 12.sp, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildSignalWidget(TradingSignal signal) {
    final isLong = signal.signalType == 'LONG';
    final color = isLong ? Colors.green : Colors.red;

    return Center(
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: color, width: 3),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 신호 아이콘
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(color: color, shape: BoxShape.circle),
              child: Icon(
                isLong ? Icons.trending_up : Icons.trending_down,
                size: 40.w,
                color: Colors.white,
              ),
            ),

            SizedBox(height: 20.h),

            // 신호 타입
            Text(
              isLong ? '매수 신호' : '매도 신호',
              style: TextStyle(
                fontSize: 24.sp,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: 12.h),

            // 심볼
            Text(
              signal.symbol,
              style: TextStyle(
                fontSize: 20.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),

            SizedBox(height: 16.h),

            // 시간
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                signal.detectedAt.toString().substring(0, 19),
                style: TextStyle(fontSize: 14.sp, color: Colors.white70),
              ),
            ),

            SizedBox(height: 12.h),

            // 소스
            Text(
              '소스: ${signal.source}',
              style: TextStyle(fontSize: 12.sp, color: Colors.white60),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: _toggleConnection,
              style: ElevatedButton.styleFrom(
                backgroundColor: _isConnected ? Colors.red : Colors.green,
                minimumSize: Size(0, 48.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                _isConnected ? '연결 중지' : '연결 시작',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          ElevatedButton(
            onPressed: _refreshConnection,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              minimumSize: Size(48.w, 48.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Icon(Icons.refresh, color: Colors.white, size: 20.w),
          ),
        ],
      ),
    );
  }
}
