import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'screens/mobile_signal_receiver.dart';
import 'services/signal_receiver_service.dart';

void main() {
  runApp(const OpenSystemsBotApp());
}

class OpenSystemsBotApp extends StatelessWidget {
  const OpenSystemsBotApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (context, child) {
        return MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => SignalReceiverService()),
          ],
          child: MaterialApp(
            title: '오픈시스템즈 봇',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              brightness: Brightness.dark,
              scaffoldBackgroundColor: Colors.black,
              appBarTheme: const AppBarTheme(
                backgroundColor: Colors.black,
                elevation: 0,
              ),
            ),
            home: const MobileSignalReceiver(),
          ),
        );
      },
    );
  }
}
